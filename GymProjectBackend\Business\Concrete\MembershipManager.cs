using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Business;
using Core.Utilities.Results;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Linq;
using System.Transactions;

namespace Business.Concrete
{
    public class MembershipManager : IMembershipService
    {
        IMembershipDal _membershipDal;
        IPaymentDal _paymentDal;
        IRemainingDebtDal _remainingDebtDal;
        IMembershipFreezeHistoryService _freezeHistoryService;

        public MembershipManager(IMembershipDal membershipDal,IPaymentDal paymentDal,IRemainingDebtDal remainingDebtDal, IMembershipFreezeHistoryService freezeHistoryService)
        {
            _membershipDal = membershipDal;
            _paymentDal = paymentDal;
            _remainingDebtDal = remainingDebtDal;
            _freezeHistoryService = freezeHistoryService;

        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Membership")]
        public IResult CancelFreeze(int membershipId)
        {
            if (!_membershipDal.IsMembershipFrozen(membershipId))
                return new ErrorResult("Üyelik dondurulmamış durumda.");

            var membership = _membershipDal.Get(m => m.MembershipID == membershipId);
            if (membership == null)
                return new ErrorResult("Üyelik bulunamadı.");

            // En son dondurma kaydını bul ve güncelle
            var freezeHistories = _freezeHistoryService.GetByMembershipId(membershipId).Data;
            var lastFreezeHistory = freezeHistories.OrderByDescending(x => x.CreationDate).FirstOrDefault();

            if (lastFreezeHistory != null)
            {
                var history = new MembershipFreezeHistory
                {
                    FreezeHistoryID = lastFreezeHistory.FreezeHistoryID,
                    MembershipID = membershipId,
                    StartDate = lastFreezeHistory.StartDate,
                    PlannedEndDate = lastFreezeHistory.PlannedEndDate,
                    ActualEndDate = DateTime.Now,
                    FreezeDays = lastFreezeHistory.FreezeDays,
                    UsedDays = 0, // Tamamen iptal edildiği için kullanılan gün 0
                    CancellationType = "Tamamen İptal",
                    CreationDate = lastFreezeHistory.CreationDate
                };

                _freezeHistoryService.Update(history);
            }

            _membershipDal.CancelFreeze(membershipId);
            return new SuccessResult("Üyelik dondurma işlemi tamamen iptal edildi.");
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Membership")]
        public IResult ReactivateFromToday(int membershipId)
        {
            if (!_membershipDal.IsMembershipFrozen(membershipId))
                return new ErrorResult("Üyelik dondurulmamış durumda.");

            var membership = _membershipDal.Get(m => m.MembershipID == membershipId);
            if (membership == null)
                return new ErrorResult("Üyelik bulunamadı.");

            // En son dondurma kaydını bul ve güncelle
            var freezeHistories = _freezeHistoryService.GetByMembershipId(membershipId).Data;
            var lastFreezeHistory = freezeHistories.OrderByDescending(x => x.CreationDate).FirstOrDefault();

            if (lastFreezeHistory != null)
            {
                var usedDays = (int)(DateTime.Now - lastFreezeHistory.StartDate).TotalDays;

                var history = new MembershipFreezeHistory
                {
                    FreezeHistoryID = lastFreezeHistory.FreezeHistoryID,
                    MembershipID = membershipId,
                    StartDate = lastFreezeHistory.StartDate,
                    PlannedEndDate = lastFreezeHistory.PlannedEndDate,
                    ActualEndDate = DateTime.Now,
                    FreezeDays = lastFreezeHistory.FreezeDays,
                    UsedDays = usedDays,
                    CancellationType = "Erken Başlatma",
                    CreationDate = lastFreezeHistory.CreationDate
                };

                _freezeHistoryService.Update(history);
            }

            _membershipDal.ReactivateFromToday(membershipId);
            return new SuccessResult("Üyelik bugünden itibaren aktif edildi.");
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Membership")]
        public IResult FreezeMembership(MembershipFreezeRequestDto freezeRequest)
        {
            if (freezeRequest.FreezeDays < 1 || freezeRequest.FreezeDays > 365)
                return new ErrorResult(Messages.FreezeDaysInvalid);

            var remainingDaysResult = _freezeHistoryService.GetRemainingFreezeDays(freezeRequest.MembershipID);
            if (!remainingDaysResult.Success || remainingDaysResult.Data < freezeRequest.FreezeDays)
                return new ErrorResult("Yıllık dondurma hakkınız yetersiz");

            if (_membershipDal.IsMembershipFrozen(freezeRequest.MembershipID))
                return new ErrorResult(Messages.MembershipAlreadyFrozen);

            var freezeStartDate = DateTime.Now;
            // Bitiş tarihini gün olarak ayarla ve saati 00:01 olarak belirle
            var freezeEndDate = freezeStartDate.AddDays(freezeRequest.FreezeDays)
                .Date
                .AddHours(0)
                .AddMinutes(1);

            // Üyeliği dondur
            _membershipDal.FreezeMembership(freezeRequest.MembershipID, freezeRequest.FreezeDays);

            // Dondurma geçmişine kaydet
            var freezeHistory = new MembershipFreezeHistory
            {
                MembershipID = freezeRequest.MembershipID,
                StartDate = freezeStartDate,
                PlannedEndDate = freezeEndDate,
                FreezeDays = freezeRequest.FreezeDays,
                CreationDate = DateTime.Now
            };

            _freezeHistoryService.Add(freezeHistory);

            return new SuccessResult(Messages.MembershipFrozen);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Membership")]
        public IResult UnfreezeMembership(int membershipId)
        {
            if (!_membershipDal.IsMembershipFrozen(membershipId))
                return new ErrorResult(Messages.MembershipAlreadyFrozen);

            _membershipDal.UnfreezeMembership(membershipId);
            return new SuccessResult(Messages.MembershipUnfrozen);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "Membership", "Frozen")]
        public IDataResult<List<MembershipFreezeDto>> GetFrozenMemberships()
        {
            return new SuccessDataResult<List<MembershipFreezeDto>>(_membershipDal.GetFrozenMemberships());
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Membership")]
        public IResult Add(MembershipAddDto membershipDto)
        {
            // Akıllı yenileme mantığı: Aynı branş ve paket türü kontrolü
            var existingMembership = _membershipDal.Get(m =>
                m.MemberID == membershipDto.MemberID &&
                m.MembershipTypeID == membershipDto.MembershipTypeID &&
                m.EndDate >= DateTime.Now &&
                m.IsActive == true
            );

            string paymentStatus = membershipDto.PaymentMethod== "Borç" ? "Pending" : "Completed";

            // Eğer zorla yeni üyelik oluşturma isteniyorsa, mevcut üyeliği yok say
            if (membershipDto.ForceNewMembership)
            {
                existingMembership = null;
            }

            if (existingMembership != null && !membershipDto.ForceNewMembership)
            {
                using (var scope = new TransactionScope())
                {
                    try
                    {
                        // Aynı paket türü - sadece süre uzat ve MembershipTypeID'yi güncelle
                        existingMembership.EndDate = existingMembership.EndDate.AddDays(membershipDto.Day);
                        existingMembership.MembershipTypeID = membershipDto.MembershipTypeID; // ÖNEMLİ: Bu satır eklendi!
                        existingMembership.UpdatedDate = DateTime.Now;
                        _membershipDal.Update(existingMembership);

                        Payment payment = new Payment
                        {
                            PaymentAmount = membershipDto.Price,
                            PaymentMethod = membershipDto.PaymentMethod,
                            OriginalPaymentMethod = membershipDto.PaymentMethod,  // İlk ödeme tipi
                            FinalPaymentMethod = membershipDto.PaymentMethod,     // Başlangıçta aynı
                            PaymentDate = DateTime.Now,
                            MemberShipID = existingMembership.MembershipID,
                            PaymentStatus = paymentStatus,
                            CreationDate = DateTime.Now,
                            IsActive = true
                        };
                        _paymentDal.Add(payment);

                        // Eğer ödeme yöntemi borç ise RemainingDebts tablosuna kayıt at
                        if (membershipDto.PaymentMethod == "Borç")
                        {
                            RemainingDebt remainingDebt = new RemainingDebt
                            {
                                PaymentID = payment.PaymentID,
                                OriginalAmount = membershipDto.Price,
                                RemainingAmount = membershipDto.Price,
                                LastUpdateDate = DateTime.Now,
                                IsActive = true,
                                CreationDate = DateTime.Now
                            };
                            _remainingDebtDal.Add(remainingDebt);
                        }

                        scope.Complete();
                        return new SuccessResult(Messages.MembershipUpdated);
                    }
                    catch
                    {
                        scope.Dispose();
                        return new ErrorResult(Messages.MembershipNotFound);
                    }
                }
            }
            else
            {
                using (var scope = new TransactionScope())
                {
                    try
                    {
                        Membership newMembership = new Membership
                        {
                            MemberID = membershipDto.MemberID,
                            MembershipTypeID = membershipDto.MembershipTypeID,
                            StartDate = membershipDto.StartDate,
                            EndDate = membershipDto.EndDate,
                            CreationDate = DateTime.Now,
                            IsActive = true
                        };
                        _membershipDal.Add(newMembership);

                        Payment payment = new Payment
                        {
                            PaymentAmount = membershipDto.Price,
                            PaymentMethod = membershipDto.PaymentMethod,
                            OriginalPaymentMethod = membershipDto.PaymentMethod,  // İlk ödeme tipi
                            FinalPaymentMethod = membershipDto.PaymentMethod,     // Başlangıçta aynı
                            PaymentDate = DateTime.Now,
                            MemberShipID = newMembership.MembershipID,
                            PaymentStatus = paymentStatus,
                            CreationDate = DateTime.Now,
                            IsActive = true
                        };
                        _paymentDal.Add(payment);

                        // Eğer ödeme yöntemi borç ise RemainingDebts tablosuna kayıt at
                        if (membershipDto.PaymentMethod == "Borç")
                        {
                            RemainingDebt remainingDebt = new RemainingDebt
                            {
                                PaymentID = payment.PaymentID,
                                OriginalAmount = membershipDto.Price,
                                RemainingAmount = membershipDto.Price,
                                LastUpdateDate = DateTime.Now,
                                IsActive = true,
                                CreationDate = DateTime.Now
                            };
                            _remainingDebtDal.Add(remainingDebt);
                        }

                        scope.Complete();
                        return new SuccessResult(Messages.MembershipAdded);
                    }
                    catch
                    {
                        scope.Dispose();
                        return new ErrorResult(Messages.MembershipNotFound);
                    }
                }
            }
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Membership")]
        public IResult Update(MembershipUpdateDto membershipDto)
        {
            var membership = _membershipDal.Get(m => m.MembershipID == membershipDto.MembershipID);

            if (membership == null)
            {
                return new ErrorResult(Messages.MembershipNotFound);
            }

            membership.MembershipTypeID = membershipDto.MembershipTypeID;
            membership.StartDate = membershipDto.StartDate;
            membership.EndDate = membershipDto.EndDate;
            membership.UpdatedDate = DateTime.Now; // UpdatedDate alanını güncelle

            _membershipDal.Update(membership);
            return new SuccessResult(Messages.MembershipUpdated);
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Membership")]
        public IResult Delete(int id)
        {
            using (var scope = new TransactionScope())
            {
                try
                {
                    var membership = _membershipDal.Get(m => m.MembershipID == id);
                    if (membership == null)
                    {
                        return new ErrorResult(Messages.MembershipNotFound);
                    }

                    var payments = _paymentDal.GetAll(p => p.MemberShipID == id);
                    foreach (var payment in payments)
                    {
                        // Ödemeye ait borç kaydını bul ve sil
                        var remainingDebt = _remainingDebtDal.Get(rd => rd.PaymentID == payment.PaymentID);
                        if (remainingDebt != null)
                        {
                            remainingDebt.IsActive = false;
                            _remainingDebtDal.Update(remainingDebt);
                        }

                        _paymentDal.Delete(payment.PaymentID);
                    }

                    _membershipDal.Delete(id);

                    scope.Complete();
                    return new SuccessResult(Messages.MembershipDeleted);
                }
                catch (Exception ex)
                {
                    return new ErrorResult($"Üyelik silinirken bir hata oluştu: {ex.Message}");
                }
            }
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "Membership", "All")]
        public IDataResult<List<Membership>> GetAll()
        {
            return new SuccessDataResult<List<Membership>>(_membershipDal.GetAll());
        }
        [SecuredOperation("owner,admin")]
        public IDataResult<List<Membership>> GetByMembershipId(int memberid)
        {
            return new SuccessDataResult<List<Membership>>(_membershipDal.GetAll(c => c.MembershipID == memberid));
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "Membership", "LastInfo")]
        public IDataResult<LastMembershipInfoDto> GetLastMembershipInfo(int memberId)
        {
            var lastMembership = _membershipDal.GetAll(m => m.MemberID == memberId && m.DeletedDate == null)
                                              .OrderByDescending(m => m.EndDate)
                                              .FirstOrDefault();
            if (lastMembership != null)
            {
                var now = DateTime.Now;
                var daysRemaining = Math.Ceiling((lastMembership.EndDate - now).TotalDays);
                var isActive = daysRemaining > 0;
                return new SuccessDataResult<LastMembershipInfoDto>(new LastMembershipInfoDto
                {
                    LastEndDate = lastMembership.EndDate,
                    DaysRemaining = (int)daysRemaining,
                    IsActive = isActive
                });
            }
            return new SuccessDataResult<LastMembershipInfoDto>(new LastMembershipInfoDto
            {
                LastEndDate = null,
                DaysRemaining = 0,
                IsActive = false
            });
        }

        // Çoklu branş ve akıllı yenileme için yeni metodlar
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [ValidationAspect(typeof(MembershipRenewalValidator))]
        [SmartCacheRemoveAspect("Membership")]
        public IResult SmartRenewal(MembershipRenewalDto renewalDto)
        {
            using (var scope = new TransactionScope())
            {
                try
                {
                    var currentMembership = _membershipDal.Get(m => m.MembershipID == renewalDto.CurrentMembershipID);
                    if (currentMembership == null)
                    {
                        return new ErrorResult("Mevcut üyelik bulunamadı.");
                    }

                    string paymentStatus = renewalDto.PaymentMethod == "Borç" ? "Pending" : "Completed";

                    if (renewalDto.IsSamePackage && renewalDto.IsSameBranch)
                    {
                        // Aynı paket ve branş - mevcut üyeliği uzat
                        currentMembership.EndDate = currentMembership.EndDate.AddDays(renewalDto.Day);
                        currentMembership.UpdatedDate = DateTime.Now;
                        _membershipDal.Update(currentMembership);

                        // Ödeme kaydı oluştur
                        var payment = CreatePayment(renewalDto.Price, renewalDto.PaymentMethod,
                                                  currentMembership.MembershipID, paymentStatus);
                        _paymentDal.Add(payment);

                        if (renewalDto.PaymentMethod == "Borç")
                        {
                            CreateRemainingDebt(payment.PaymentID, renewalDto.Price);
                        }

                        scope.Complete();
                        return new SuccessResult("Üyelik başarıyla uzatıldı.");
                    }
                    else
                    {
                        // Farklı paket veya branş - yeni üyelik oluştur
                        var newMembership = new Membership
                        {
                            MemberID = renewalDto.MemberID,
                            MembershipTypeID = renewalDto.NewMembershipTypeID,
                            StartDate = renewalDto.NewStartDate,
                            EndDate = renewalDto.NewEndDate,
                            CreationDate = DateTime.Now,
                            IsActive = true
                        };
                        _membershipDal.Add(newMembership);

                        // Ödeme kaydı oluştur
                        var payment = CreatePayment(renewalDto.Price, renewalDto.PaymentMethod,
                                                  newMembership.MembershipID, paymentStatus);
                        _paymentDal.Add(payment);

                        if (renewalDto.PaymentMethod == "Borç")
                        {
                            CreateRemainingDebt(payment.PaymentID, renewalDto.Price);
                        }

                        scope.Complete();
                        return new SuccessResult("Yeni üyelik başarıyla oluşturuldu.");
                    }
                }
                catch (Exception ex)
                {
                    return new ErrorResult($"Yenileme işlemi sırasında hata oluştu: {ex.Message}");
                }
            }
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 15, "Membership", "MultiBranch")]
        public IDataResult<MultiBranchMembershipDto> GetMemberMultiBranchMemberships(int memberId)
        {
            var result = _membershipDal.GetMemberMultiBranchMemberships(memberId);
            return new SuccessDataResult<MultiBranchMembershipDto>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<ActiveMembershipDetailDto>> GetActiveMembershipsByMember(int memberId)
        {
            var result = _membershipDal.GetActiveMembershipsByMember(memberId);
            return new SuccessDataResult<List<ActiveMembershipDetailDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<MembershipDeleteOptionsDto> GetMembershipDeleteOptions(int memberId)
        {
            var deletableMemberships = _membershipDal.GetDeletableMembershipsByMember(memberId);

            var result = new MembershipDeleteOptionsDto
            {
                MemberID = memberId,
                DeletableMemberships = deletableMemberships,
                HasMultipleMemberships = deletableMemberships.Count > 1,
                TotalRefundAmount = deletableMemberships.Sum(m => m.PossibleRefundAmount)
            };

            if (deletableMemberships.Any())
            {
                var firstMembership = deletableMemberships.First();
                result.MemberName = firstMembership.Branch; // Bu alanda member name olacak, DAL'da düzeltilecek
                result.PhoneNumber = ""; // Bu da DAL'da düzeltilecek
            }

            if (result.HasMultipleMemberships)
            {
                result.WarningMessage = "Bu üyenin birden fazla aktif üyeliği bulunmaktadır. Lütfen silmek istediğiniz üyeliği seçiniz.";
            }

            return new SuccessDataResult<MembershipDeleteOptionsDto>(result);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Membership")]
        public IResult DeleteSpecificMembership(int membershipId, bool confirmDelete = false)
        {
            if (!confirmDelete)
            {
                return new ErrorResult("Silme işlemi onaylanmadı.");
            }

            return Delete(membershipId); // Mevcut Delete metodunu kullan
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<bool> HasMultipleMemberships(int memberId)
        {
            var result = _membershipDal.HasMultipleMemberships(memberId);
            return new SuccessDataResult<bool>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<MembershipRenewalDto> AnalyzeRenewalOptions(int memberId, int newMembershipTypeId)
        {
            // Mevcut aktif üyelikleri al
            var activeMemberships = _membershipDal.GetActiveMembershipsByMember(memberId);

            if (!activeMemberships.Any())
            {
                return new ErrorDataResult<MembershipRenewalDto>("Üyenin aktif üyeliği bulunamadı.");
            }

            // Yeni üyelik türü bilgisini al
            var newMembershipType = _membershipDal.GetMembershipType(newMembershipTypeId);
            if (newMembershipType == null)
            {
                return new ErrorDataResult<MembershipRenewalDto>("Geçersiz üyelik türü.");
            }

            // En son bitiş tarihine sahip üyeliği bul
            var latestMembership = activeMemberships.OrderByDescending(m => m.EndDate).First();

            // Aynı branş ve paket kontrolü
            bool isSameBranch = activeMemberships.Any(m => m.Branch == newMembershipType.Branch);
            bool isSamePackage = activeMemberships.Any(m => m.MembershipTypeID == newMembershipTypeId);

            var renewalDto = new MembershipRenewalDto
            {
                MemberID = memberId,
                CurrentMembershipID = latestMembership.MembershipID,
                CurrentMembershipTypeID = latestMembership.MembershipTypeID,
                NewMembershipTypeID = newMembershipTypeId,
                CurrentEndDate = latestMembership.EndDate,
                IsSameBranch = isSameBranch,
                IsSamePackage = isSamePackage,
                RenewalType = DetermineRenewalType(isSameBranch, isSamePackage)
            };

            return new SuccessDataResult<MembershipRenewalDto>(renewalDto);
        }

        // Yardımcı metodlar
        private Payment CreatePayment(decimal amount, string paymentMethod, int membershipId, string paymentStatus)
        {
            return new Payment
            {
                PaymentAmount = amount,
                PaymentMethod = paymentMethod,
                OriginalPaymentMethod = paymentMethod,
                FinalPaymentMethod = paymentMethod,
                PaymentDate = DateTime.Now,
                MemberShipID = membershipId,
                PaymentStatus = paymentStatus,
                CreationDate = DateTime.Now,
                IsActive = true
            };
        }

        private void CreateRemainingDebt(int paymentId, decimal amount)
        {
            var remainingDebt = new RemainingDebt
            {
                PaymentID = paymentId,
                OriginalAmount = amount,
                RemainingAmount = amount,
                LastUpdateDate = DateTime.Now,
                IsActive = true,
                CreationDate = DateTime.Now
            };
            _remainingDebtDal.Add(remainingDebt);
        }

        private string DetermineRenewalType(bool isSameBranch, bool isSamePackage)
        {
            if (isSameBranch && isSamePackage)
                return "EXTEND";
            else if (isSameBranch && !isSamePackage)
                return "UPGRADE";
            else
                return "CHANGE_BRANCH";
        }
    }
}
