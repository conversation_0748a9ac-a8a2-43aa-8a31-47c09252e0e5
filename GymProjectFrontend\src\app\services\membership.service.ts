import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ListResponseModel } from '../models/listResponseModel';
import { Membership } from '../models/membership';
import { ResponseModel } from '../models/responseModel';
import { BaseApiService } from './baseApiService';
import { membershipUpdate } from '../models/membershipUpdate';
import { MembershipRenewal } from '../models/membershipRenewal';
import { MultiBranchMembership, ActiveMembershipDetail } from '../models/multiBranchMembership';
import { MembershipDeleteOptions } from '../models/membershipDeleteOptions';
import { SingleResponseModel } from '../models/singleResponseModel';

@Injectable({
  providedIn: 'root',
})
export class MembershipService extends BaseApiService {
  constructor(private httpClient: HttpClient) {
    super();
  }
  freezeMembership(
    membershipId: number,
    freezeDays: number
  ): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      `${this.apiUrl}membership/freeze`,
      { membershipID: membershipId, freezeDays: freezeDays }
    );
  }

  unfreezeMembership(membershipId: number): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      `${this.apiUrl}membership/unfreeze/${membershipId}`,
      {}
    );
  }

  getFrozenMemberships(): Observable<ListResponseModel<any>> {
    return this.httpClient.get<ListResponseModel<any>>(
      `${this.apiUrl}membership/frozen`
    );
  }

  add(membership: Membership): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      this.apiUrl + 'membership/add',
      membership
    );
  }
  update(membership: membershipUpdate): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      this.apiUrl + 'membership/update',
      membership
    );
  }
  delete(membershipID: number): Observable<ResponseModel> {
    let deletePath = `${this.apiUrl}membership/delete/?id=${membershipID}`;
    return this.httpClient.delete<ResponseModel>(deletePath);
  }
  getMembershipById(membershipId: number): Observable<ResponseModel> {
    return this.httpClient.get<ResponseModel>(
      `${this.apiUrl}membership/getbymembershipid?id=${membershipId}`
    );
  }
  getLastMembershipInfo(memberId: number): Observable<any> {
    return this.httpClient.get<any>(
      `${this.apiUrl}membership/getlastmembershipinfo/${memberId}`
    );
  }
  cancelFreeze(membershipId: number): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      `${this.apiUrl}membership/cancel-freeze/${membershipId}`,
      {}
    );
  }

  reactivateFromToday(membershipId: number): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      `${this.apiUrl}membership/reactivate-from-today/${membershipId}`,
      {}
    );
  }

  // Çoklu branş ve akıllı yenileme için yeni metodlar
  smartRenewal(renewalData: MembershipRenewal): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(
      `${this.apiUrl}membership/smart-renewal`,
      renewalData
    );
  }

  getMemberMultiBranchMemberships(memberId: number): Observable<SingleResponseModel<MultiBranchMembership>> {
    return this.httpClient.get<SingleResponseModel<MultiBranchMembership>>(
      `${this.apiUrl}membership/multi-branch/${memberId}`
    );
  }

  getActiveMembershipsByMember(memberId: number): Observable<ListResponseModel<ActiveMembershipDetail>> {
    return this.httpClient.get<ListResponseModel<ActiveMembershipDetail>>(
      `${this.apiUrl}membership/active-memberships/${memberId}`
    );
  }

  getMembershipDeleteOptions(memberId: number): Observable<SingleResponseModel<MembershipDeleteOptions>> {
    return this.httpClient.get<SingleResponseModel<MembershipDeleteOptions>>(
      `${this.apiUrl}membership/delete-options/${memberId}`
    );
  }

  deleteSpecificMembership(membershipId: number, confirmDelete: boolean = false): Observable<ResponseModel> {
    return this.httpClient.delete<ResponseModel>(
      `${this.apiUrl}membership/delete-specific/${membershipId}?confirmDelete=${confirmDelete}`
    );
  }

  hasMultipleMemberships(memberId: number): Observable<SingleResponseModel<boolean>> {
    return this.httpClient.get<SingleResponseModel<boolean>>(
      `${this.apiUrl}membership/has-multiple/${memberId}`
    );
  }

  analyzeRenewalOptions(memberId: number, newMembershipTypeId: number): Observable<SingleResponseModel<MembershipRenewal>> {
    return this.httpClient.get<SingleResponseModel<MembershipRenewal>>(
      `${this.apiUrl}membership/analyze-renewal/${memberId}/${newMembershipTypeId}`
    );
  }
}
