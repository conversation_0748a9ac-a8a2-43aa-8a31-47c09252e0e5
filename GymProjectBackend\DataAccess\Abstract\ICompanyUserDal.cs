﻿using Core.DataAccess;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface ICompanyUserDal:IEntityRepository<CompanyUser>
    {
        List<CompanyDetailDto> GetCompanyDetails();
        List<CompanyDetailDto> GetCompanyUserDetailsByCityId(int cityId);
        List<CompanyUserDetailDto> GetCompanyUserDetails();


    }
}
