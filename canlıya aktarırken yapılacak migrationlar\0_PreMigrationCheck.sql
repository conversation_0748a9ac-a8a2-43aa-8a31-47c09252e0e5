-- =============================================
-- ÖN KONTROL SCRİPTİ - MİGRATİON ÖNCESİ ÇALIŞTIR
-- =============================================

USE [GymProject]
GO

PRINT '============================================='
PRINT 'ÇOKLU BRANŞ ÜYELİK SİSTEMİ - ÖN KONTROL'
PRINT '============================================='

-- =============================================
-- 1. MEVCUT TABLO YAPILARINI KONTROL ET
-- =============================================

PRINT 'Mevcut tablolar kontrol ediliyor...'

-- Temel tabloların varlığını kontrol et
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Memberships')
    PRINT '✓ Memberships tablosu mevcut'
ELSE
    PRINT '✗ Memberships tablosu bulunamadı!'

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Payments')
    PRINT '✓ Payments tablosu mevcut'
ELSE
    PRINT '✗ Payments tablosu bulunamadı!'

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Members')
    PRINT '✓ Members tablosu mevcut'
ELSE
    PRINT '✗ Members tablosu bulunamadı!'

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'MembershipTypes')
    PRINT '✓ MembershipTypes tablosu mevcut'
ELSE
    PRINT '✗ MembershipTypes tablosu bulunamadı!'

IF EXISTS (SELECT * FROM sys.tables WHERE name = 'EntryExitHistories')
    PRINT '✓ EntryExitHistories tablosu mevcut'
ELSE
    PRINT '✗ EntryExitHistories tablosu bulunamadı!'

-- =============================================
-- 2. MEVCUT ALAN YAPILARINI KONTROL ET
-- =============================================

PRINT ''
PRINT 'Tablo alanları kontrol ediliyor...'

-- Memberships tablosu alanları
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Memberships') AND name = 'IsFrozen')
    PRINT '✓ Memberships.IsFrozen alanı mevcut'
ELSE
    PRINT '✗ Memberships.IsFrozen alanı bulunamadı!'

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Memberships') AND name = 'CompanyID')
    PRINT '✓ Memberships.CompanyID alanı mevcut'
ELSE
    PRINT '✗ Memberships.CompanyID alanı bulunamadı!'

-- Payments tablosu alanları
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Payments') AND name = 'MemberShipID')
    PRINT '✓ Payments.MemberShipID alanı mevcut'
ELSE
    PRINT '✗ Payments.MemberShipID alanı bulunamadı!'

-- =============================================
-- 3. MEVCUT İNDEXLERİ KONTROL ET
-- =============================================

PRINT ''
PRINT 'Mevcut indexler kontrol ediliyor...'

-- Oluşturulacak indexlerin zaten var olup olmadığını kontrol et
DECLARE @IndexCount INT = 0

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Memberships_CompanyID_MemberID_IsActive_EndDate')
BEGIN
    PRINT '⚠ IX_Memberships_CompanyID_MemberID_IsActive_EndDate zaten mevcut'
    SET @IndexCount = @IndexCount + 1
END

IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Payments_CompanyID_MemberShipID_IsActive')
BEGIN
    PRINT '⚠ IX_Payments_CompanyID_MemberShipID_IsActive zaten mevcut'
    SET @IndexCount = @IndexCount + 1
END

IF @IndexCount > 0
    PRINT CONCAT('Toplam ', @IndexCount, ' index zaten mevcut - migration sırasında atlanacak')
ELSE
    PRINT '✓ Yeni indexler oluşturulmaya hazır'

-- =============================================
-- 4. VERİ TUTARLILIĞI KONTROL ET
-- =============================================

PRINT ''
PRINT 'Veri tutarlılığı kontrol ediliyor...'

-- Aktif üyelik sayısı
DECLARE @ActiveMembershipCount INT
SELECT @ActiveMembershipCount = COUNT(*) 
FROM Memberships 
WHERE IsActive = 1 AND EndDate > GETDATE()

PRINT CONCAT('Aktif üyelik sayısı: ', @ActiveMembershipCount)

-- Çoklu branş üyeliği olan üye sayısı
DECLARE @MultiBranchCount INT
SELECT @MultiBranchCount = COUNT(*)
FROM (
    SELECT MemberID, COUNT(DISTINCT mt.Branch) as BranchCount
    FROM Memberships ms
    INNER JOIN MembershipTypes mt ON ms.MembershipTypeID = mt.MembershipTypeID
    WHERE ms.IsActive = 1 AND ms.EndDate > GETDATE()
    GROUP BY MemberID
    HAVING COUNT(DISTINCT mt.Branch) > 1
) MultiBranch

PRINT CONCAT('Çoklu branş üyeliği olan üye sayısı: ', @MultiBranchCount)

-- Ödeme kayıt sayısı
DECLARE @PaymentCount INT
SELECT @PaymentCount = COUNT(*) FROM Payments WHERE IsActive = 1

PRINT CONCAT('Aktif ödeme kayıt sayısı: ', @PaymentCount)

-- =============================================
-- 5. PERFORMANS BASELINE
-- =============================================

PRINT ''
PRINT 'Performans baseline ölçümü...'

DECLARE @StartTime DATETIME2 = GETDATE()

-- Test sorgusu: Aktif üyelikleri getir
SELECT COUNT(*) as TestQueryResult
FROM Memberships ms
INNER JOIN Members m ON ms.MemberID = m.MemberID
INNER JOIN MembershipTypes mt ON ms.MembershipTypeID = mt.MembershipTypeID
WHERE ms.IsActive = 1 
    AND ms.EndDate > GETDATE()
    AND ms.IsFrozen = 0

DECLARE @EndTime DATETIME2 = GETDATE()
DECLARE @Duration INT = DATEDIFF(MILLISECOND, @StartTime, @EndTime)

PRINT CONCAT('Test sorgusu süresi (migration öncesi): ', @Duration, ' ms')

-- =============================================
-- 6. BACKUP ÖNERİSİ
-- =============================================

PRINT ''
PRINT '============================================='
PRINT 'ÖNEMLİ UYARILAR:'
PRINT '============================================='
PRINT '1. Migration öncesi mutlaka database backup alın!'
PRINT '2. Test ortamında önce deneyin!'
PRINT '3. Migration sırasında sistem kullanımını durdurun!'
PRINT '4. Migration sonrası performans testleri yapın!'
PRINT ''
PRINT 'Backup komutu örneği:'
PRINT 'BACKUP DATABASE [GymProject] TO DISK = ''C:\Backup\GymProject_PreMigration.bak'''
PRINT ''
PRINT '============================================='
PRINT 'ÖN KONTROL TAMAMLANDI'
PRINT '============================================='
GO
