.compact-display {
  display: inline-flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.25rem;
}

.branch-item {
  display: inline-flex;
  align-items: center;
}

.badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.5rem;
  border-radius: 0.375rem;
  cursor: help;
  transition: all 0.2s ease;
}

.badge:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.membership-detail-card {
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border-radius: 0.5rem;
  overflow: hidden;
}

.membership-detail-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.card-header {
  border-bottom: none;
  font-weight: 600;
}

.card-title {
  font-size: 0.9rem;
  color: #495057;
  margin-bottom: 0.5rem;
}

.text-sm {
  font-size: 0.8rem;
}

.fw-bold {
  font-weight: 600 !important;
}

/* <PERSON><PERSON><PERSON> renkleri */
.bg-primary {
  background-color: #007bff !important;
}

.bg-success {
  background-color: #28a745 !important;
}

.bg-info {
  background-color: #17a2b8 !important;
}

.bg-warning {
  background-color: #ffc107 !important;
  color: #212529 !important;
}

.bg-danger {
  background-color: #dc3545 !important;
}

.bg-secondary {
  background-color: #6c757d !important;
}

.bg-dark {
  background-color: #343a40 !important;
}

/* Kalan gün renkleri */
.text-danger {
  color: #dc3545 !important;
}

.text-warning {
  color: #ffc107 !important;
}

.text-info {
  color: #17a2b8 !important;
}

.text-success {
  color: #28a745 !important;
}

/* Özet bilgi */
.summary-info .alert {
  border-radius: 0.5rem;
  border: none;
  background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
}

/* Boş durum */
.empty-state {
  padding: 2rem;
  border: 2px dashed #dee2e6;
  border-radius: 0.5rem;
  background-color: #f8f9fa;
}

/* Responsive */
@media (max-width: 768px) {
  .compact-display {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .membership-detail-card {
    margin-bottom: 1rem;
  }
  
  .text-sm {
    font-size: 0.75rem;
  }
  
  .summary-info .col-4 {
    margin-bottom: 0.5rem;
  }
}

/* Animasyonlar */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.membership-detail-card {
  animation: fadeIn 0.3s ease-out;
}

/* Tooltip benzeri hover efekti */
.badge[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.7rem;
  white-space: nowrap;
  z-index: 1000;
  margin-bottom: 0.25rem;
}

/* Dark mode desteği */
@media (prefers-color-scheme: dark) {
  .membership-detail-card {
    background-color: #2d3748;
    color: #e2e8f0;
  }
  
  .card-title {
    color: #e2e8f0;
  }
  
  .text-muted {
    color: #a0aec0 !important;
  }
  
  .empty-state {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
  }
}
