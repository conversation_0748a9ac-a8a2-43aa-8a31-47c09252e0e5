import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MembershipService } from '../../../services/membership.service';
import { ToastrService } from 'ngx-toastr';
import { MembershipDeleteOptions, DeletableMembership } from '../../../models/membershipDeleteOptions';

@Component({
  selector: 'app-membership-delete-options',
  templateUrl: './membership-delete-options.component.html',
  styleUrls: ['./membership-delete-options.component.css'],
  standalone: false
})
export class MembershipDeleteOptionsComponent implements OnInit {
  deleteOptions: MembershipDeleteOptions | null = null;
  selectedMembership: DeletableMembership | null = null;
  isLoading = true;
  isDeleting = false;

  constructor(
    public dialogRef: MatDialogRef<MembershipDeleteOptionsComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { memberId: number, memberName: string },
    private membershipService: MembershipService,
    private toastrService: ToastrService
  ) {}

  ngOnInit(): void {
    this.loadDeleteOptions();
  }

  loadDeleteOptions(): void {
    this.isLoading = true;
    this.membershipService.getMembershipDeleteOptions(this.data.memberId).subscribe({
      next: (response) => {
        if (response.success) {
          this.deleteOptions = response.data;
          
          // Eğer tek üyelik varsa otomatik seç
          if (this.deleteOptions.deletableMemberships.length === 1) {
            this.selectedMembership = this.deleteOptions.deletableMemberships[0];
          }
        } else {
          this.toastrService.error(response.message, 'Hata');
          this.dialogRef.close(false);
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.toastrService.error('Üyelik bilgileri yüklenirken hata oluştu.', 'Hata');
        this.isLoading = false;
        this.dialogRef.close(false);
      }
    });
  }

  selectMembership(membership: DeletableMembership): void {
    this.selectedMembership = membership;
  }

  confirmDelete(): void {
    if (!this.selectedMembership) {
      this.toastrService.warning('Lütfen silmek istediğiniz üyeliği seçiniz.', 'Uyarı');
      return;
    }

    if (!this.selectedMembership.canBeDeleted) {
      this.toastrService.error(this.selectedMembership.reasonCannotDelete, 'Silinemez');
      return;
    }

    this.isDeleting = true;
    this.membershipService.deleteSpecificMembership(this.selectedMembership.membershipID, true).subscribe({
      next: (response) => {
        if (response.success) {
          this.toastrService.success(response.message, 'Başarılı');
          this.dialogRef.close(true);
        } else {
          this.toastrService.error(response.message, 'Hata');
        }
        this.isDeleting = false;
      },
      error: (error) => {
        this.toastrService.error('Üyelik silinirken hata oluştu.', 'Hata');
        this.isDeleting = false;
      }
    });
  }

  cancel(): void {
    this.dialogRef.close(false);
  }

  getRemainingDaysColor(remainingDays: number): string {
    if (remainingDays <= 0) return 'text-danger';
    if (remainingDays <= 7) return 'text-warning';
    if (remainingDays <= 30) return 'text-info';
    return 'text-success';
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(amount);
  }

  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString('tr-TR');
  }
}
