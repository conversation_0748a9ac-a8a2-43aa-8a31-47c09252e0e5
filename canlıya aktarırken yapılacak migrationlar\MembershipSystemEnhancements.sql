-- =============================================
-- ÜYELİK SİSTEMİ İYİLEŞTİRMELERİ
-- Ak<PERSON>ll<PERSON> yenileme, çoklu branş ve güvenli silme için
-- =============================================

USE [GymProject]
GO

-- =============================================
-- 1. MEMBERSHIP TABLOSUNA YENİ ALANLAR (Gerekirse)
-- =============================================

-- RenewalType alanı ekle (opsiyonel - DTO'da yönetiliyor)
-- Mevcut Membership entity'de bu alan yok, gerekirse eklenebilir
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[Memberships]') AND name = 'RenewalType')
BEGIN
    ALTER TABLE [dbo].[Memberships]
    ADD [RenewalType] NVARCHAR(50) NULL

    PRINT 'RenewalType alanı Memberships tablosuna eklendi'
END
ELSE
BEGIN
    PRINT 'RenewalType alanı zaten mevcut - atlanıyor'
END
GO

-- OriginalMembershipID alanı ekle (yenileme zinciri takibi için)
-- Bu alan yenileme geçmişini takip etmek için kullanılabilir
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('[dbo].[Memberships]') AND name = 'OriginalMembershipID')
BEGIN
    ALTER TABLE [dbo].[Memberships]
    ADD [OriginalMembershipID] INT NULL

    PRINT 'OriginalMembershipID alanı Memberships tablosuna eklendi'
END
ELSE
BEGIN
    PRINT 'OriginalMembershipID alanı zaten mevcut - atlanıyor'
END
GO

-- =============================================
-- 2. PERFORMANS İÇİN COMPUTED COLUMNS (ATLANMIŞ)
-- =============================================

-- Computed column'lar GETDATE() kullandığı için non-deterministic
-- Bu nedenle PERSISTED olamaz ve gerçek performans faydası sağlamaz
-- Bunun yerine application layer'da hesaplama yapılacak

PRINT 'Computed column''lar atlandı - application layer''da hesaplanacak'
GO

-- =============================================
-- 3. ÇOKLU BRANŞ DESTEĞİ İÇİN VIEW
-- =============================================

-- Çoklu branş üyelik görünümü
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_MultiBranchMemberships')
    DROP VIEW [dbo].[vw_MultiBranchMemberships]
GO

CREATE VIEW [dbo].[vw_MultiBranchMemberships]
AS
SELECT 
    m.MemberID,
    m.Name AS MemberName,
    m.PhoneNumber,
    m.Gender,
    m.CompanyID,
    COUNT(ms.MembershipID) AS TotalActiveMemberships,
    STRING_AGG(mt.Branch, ', ') AS AllBranches,
    STRING_AGG(CONCAT(mt.Branch, '(', 
        CASE 
            WHEN ms.EndDate > GETDATE() 
            THEN DATEDIFF(DAY, GETDATE(), ms.EndDate)
            ELSE 0 
        END, ')'), ', ') AS CombinedBranchDisplay,
    MIN(ms.StartDate) AS EarliestStartDate,
    MAX(ms.EndDate) AS LatestEndDate,
    CASE WHEN COUNT(DISTINCT mt.Branch) > 1 THEN 1 ELSE 0 END AS HasMultipleBranches,
    MAX(ms.UpdatedDate) AS LastUpdateDate
FROM [dbo].[Members] m
INNER JOIN [dbo].[Memberships] ms ON m.MemberID = ms.MemberID
INNER JOIN [dbo].[MembershipTypes] mt ON ms.MembershipTypeID = mt.MembershipTypeID
WHERE ms.IsActive = 1
    AND ms.EndDate > GETDATE()
    AND ms.IsFrozen = 0  -- Dondurulmamış üyelikler
    AND m.IsActive = 1
    AND m.CompanyID = ms.CompanyID  -- CompanyID tutarlılığı
    AND ms.CompanyID = mt.CompanyID  -- CompanyID tutarlılığı
GROUP BY m.MemberID, m.Name, m.PhoneNumber, m.Gender, m.CompanyID
GO

PRINT 'vw_MultiBranchMemberships view oluşturuldu'
GO

-- =============================================
-- 4. AKILLI YENİLEME İÇİN STORED PROCEDURE
-- =============================================

IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_AnalyzeMembershipRenewal')
    DROP PROCEDURE [dbo].[sp_AnalyzeMembershipRenewal]
GO

CREATE PROCEDURE [dbo].[sp_AnalyzeMembershipRenewal]
    @MemberID INT,
    @NewMembershipTypeID INT,
    @CompanyID INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @IsSameBranch BIT = 0
    DECLARE @IsSamePackage BIT = 0
    DECLARE @RenewalType NVARCHAR(50)
    DECLARE @CurrentMembershipID INT
    DECLARE @NewBranch NVARCHAR(100)
    
    -- Yeni üyelik türü bilgisini al
    SELECT @NewBranch = Branch 
    FROM MembershipTypes 
    WHERE MembershipTypeID = @NewMembershipTypeID AND CompanyID = @CompanyID
    
    -- Mevcut aktif üyelikleri kontrol et
    SELECT TOP 1 @CurrentMembershipID = MembershipID
    FROM Memberships
    WHERE MemberID = @MemberID
        AND IsActive = 1
        AND EndDate >= GETDATE()
        AND IsFrozen = 0  -- Dondurulmamış üyelikler
        AND CompanyID = @CompanyID
    ORDER BY EndDate DESC
    
    -- Aynı branş kontrolü
    IF EXISTS (
        SELECT 1 FROM Memberships ms
        INNER JOIN MembershipTypes mt ON ms.MembershipTypeID = mt.MembershipTypeID
        WHERE ms.MemberID = @MemberID
            AND ms.IsActive = 1
            AND ms.EndDate >= GETDATE()
            AND ms.IsFrozen = 0  -- Dondurulmamış üyelikler
            AND mt.Branch = @NewBranch
            AND ms.CompanyID = @CompanyID
            AND mt.CompanyID = @CompanyID
    )
        SET @IsSameBranch = 1
    
    -- Aynı paket kontrolü
    IF EXISTS (
        SELECT 1 FROM Memberships 
        WHERE MemberID = @MemberID
            AND MembershipTypeID = @NewMembershipTypeID
            AND IsActive = 1
            AND EndDate >= GETDATE()
            AND IsFrozen = 0  -- Dondurulmamış üyelikler
            AND CompanyID = @CompanyID
    )
        SET @IsSamePackage = 1
    
    -- Yenileme türünü belirle
    IF @IsSameBranch = 1 AND @IsSamePackage = 1
        SET @RenewalType = 'EXTEND'
    ELSE IF @IsSameBranch = 1 AND @IsSamePackage = 0
        SET @RenewalType = 'UPGRADE'
    ELSE
        SET @RenewalType = 'CHANGE_BRANCH'
    
    -- Sonuçları döndür
    SELECT 
        @MemberID AS MemberID,
        @CurrentMembershipID AS CurrentMembershipID,
        @NewMembershipTypeID AS NewMembershipTypeID,
        @IsSameBranch AS IsSameBranch,
        @IsSamePackage AS IsSamePackage,
        @RenewalType AS RenewalType,
        @NewBranch AS NewBranch
END
GO

PRINT 'sp_AnalyzeMembershipRenewal stored procedure oluşturuldu'
GO

-- =============================================
-- 5. SİLME SEÇENEKLERİ İÇİN STORED PROCEDURE
-- =============================================

IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetMembershipDeleteOptions')
    DROP PROCEDURE [dbo].[sp_GetMembershipDeleteOptions]
GO

CREATE PROCEDURE [dbo].[sp_GetMembershipDeleteOptions]
    @MemberID INT,
    @CompanyID INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        ms.MembershipID,
        ms.MembershipTypeID,
        mt.Branch,
        mt.TypeName,
        ms.StartDate,
        ms.EndDate,
        CASE 
            WHEN ms.EndDate > GETDATE() 
            THEN DATEDIFF(DAY, GETDATE(), ms.EndDate)
            ELSE 0 
        END AS RemainingDays,
        ms.IsActive,
        ms.IsFrozen,
        ISNULL(p.PaymentAmount, 0) AS PaidAmount,
        ISNULL(p.PaymentMethod, '') AS PaymentMethod,
        ISNULL(p.PaymentDate, '1900-01-01') AS PaymentDate,
        -- Basit iade hesaplaması
        CASE 
            WHEN ms.EndDate > GETDATE() 
            THEN ROUND(ISNULL(p.PaymentAmount, 0) * 
                 (CAST(DATEDIFF(DAY, GETDATE(), ms.EndDate) AS FLOAT) / 
                  CAST(DATEDIFF(DAY, ms.StartDate, ms.EndDate) AS FLOAT)), 2)
            ELSE 0 
        END AS PossibleRefundAmount,
        CASE 
            WHEN ms.EndDate > GETDATE() 
            THEN CONCAT(mt.Branch, ' - ', mt.TypeName, ' üyeliğinin ', 
                       DATEDIFF(DAY, GETDATE(), ms.EndDate), ' günü kalmıştır. Silme işlemi geri alınamaz.')
            ELSE CONCAT(mt.Branch, ' - ', mt.TypeName, ' üyeliği süresi dolmuştur.')
        END AS DeleteWarning,
        1 AS CanBeDeleted,
        '' AS ReasonCannotDelete
    FROM Memberships ms
    INNER JOIN MembershipTypes mt ON ms.MembershipTypeID = mt.MembershipTypeID
    LEFT JOIN (
        SELECT MemberShipID, PaymentAmount, PaymentMethod, PaymentDate,
               ROW_NUMBER() OVER (PARTITION BY MemberShipID ORDER BY PaymentDate DESC) as rn
        FROM Payments 
        WHERE IsActive = 1
    ) p ON ms.MembershipID = p.MemberShipID AND p.rn = 1
    WHERE ms.MemberID = @MemberID 
        AND ms.IsActive = 1
        AND ms.CompanyID = @CompanyID
        AND mt.CompanyID = @CompanyID
    ORDER BY ms.EndDate DESC
END
GO

PRINT 'sp_GetMembershipDeleteOptions stored procedure oluşturuldu'
GO

-- =============================================
-- 6. PERFORMANS İYİLEŞTİRME AYARLARI
-- =============================================

-- Auto Update Statistics
ALTER DATABASE [GymProject] SET AUTO_UPDATE_STATISTICS ON
ALTER DATABASE [GymProject] SET AUTO_UPDATE_STATISTICS_ASYNC ON

-- Query Store (SQL Server 2016+)
IF (SELECT SERVERPROPERTY('ProductMajorVersion')) >= 13
BEGIN
    ALTER DATABASE [GymProject] SET QUERY_STORE = ON
    ALTER DATABASE [GymProject] SET QUERY_STORE (OPERATION_MODE = READ_WRITE)
END

PRINT '============================================='
PRINT 'ÜYELİK SİSTEMİ İYİLEŞTİRMELERİ TAMAMLANDI'
PRINT '============================================='
PRINT 'Eklenen özellikler:'
PRINT '- Computed columns (performans)'
PRINT '- Multi-branch view'
PRINT '- Akıllı yenileme SP'
PRINT '- Silme seçenekleri SP'
PRINT '- Performans ayarları'
PRINT '============================================='
GO
