﻿﻿﻿﻿using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    // Entities/DTOs/MemberFilter.cs
    public class MemberFilter : IDto
    {
        public int MemberID { get; set; }
        public int MembershipID { get; set; }
        public string Name { get; set; }
        public string PhoneNumber { get; set; }
        public int Gender { get; set; }
        public string Branch { get; set; }
        public int RemainingDays { get; set; }
        public bool IsActive { get; set; }
        public DateTime StartDate{ get; set; }
        public DateTime EndDate{ get; set; }
        public DateTime? UpdatedDate { get; set; } // Üyeliğin güncellenme tarihi
        public bool IsFutureStartDate { get; set; } // Üyeliğin başlangıç tarihi gelecekte mi

        // Çoklu branş desteği için yeni alanlar
        public int TotalActiveMemberships { get; set; } // Toplam aktif üyelik sayısı
        public string AllBranches { get; set; } // "Fitness, Crossfit, Pilates"
        public string CombinedBranchDisplay { get; set; } // "Fitness(40), Crossfit(60)"
        public bool HasMultipleBranches { get; set; } // Birden fazla branşa üye mi?
        public int MembershipTypeID { get; set; } // Filtreleme için gerekli
    }
}
