using Core.DataAccess.EntityFramework;
using Core.Utilities.Security.CompanyContext; // ICompanyContext için eklendi
using DataAccess.Abstract;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfExpenseDal : EfCompanyEntityRepositoryBase<Expense, GymContext>, IExpenseDal
    {
        private readonly ICompanyContext _companyContext;

        public EfExpenseDal(ICompanyContext companyContext) : base(companyContext)
        {
            _companyContext = companyContext;
        }

        // Expense'e özel DAL metotları gerekirse buraya eklenebilir.
        // Örneğin, belirli bir tarih aralığındaki giderleri getiren metod:
        // public List<Expense> GetExpensesBetweenDates(DateTime startDate, DateTime endDate)
        // {
        //     using (var context = new GymContext())
        //     {
        //         int companyId = _companyContext.GetCompanyId();
        //         return context.Expenses
        //             .Where(e => e.CompanyID == companyId && e.ExpenseDate >= startDate && e.ExpenseDate <= endDate)
        //             .ToList();
        //     }
        // }
    }
}