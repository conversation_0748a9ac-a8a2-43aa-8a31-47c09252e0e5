﻿﻿using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfMembershipTypeDal : EfCompanyEntityRepositoryBase<MembershipType, GymContext>, IMembershiptypeDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        public EfMembershipTypeDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext) : base(companyContext)
        {
            _companyContext = companyContext;
        }
    }
}
