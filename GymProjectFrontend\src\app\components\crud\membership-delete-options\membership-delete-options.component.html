<div class="modal-header">
  <h4 class="modal-title">
    <i class="fas fa-trash-alt text-danger me-2"></i>
    Üyelik Silme Seçenekleri
  </h4>
  <button type="button" class="btn-close" (click)="cancel()"></button>
</div>

<div class="modal-body">
  <!-- Loading Spinner -->
  <div *ngIf="isLoading" class="text-center py-4">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Yükleniyor...</span>
    </div>
    <p class="mt-2">Üyelik bilgileri yükleniyor...</p>
  </div>

  <!-- Content -->
  <div *ngIf="!isLoading && deleteOptions">
    <!-- Member Info -->
    <div class="alert alert-info mb-3">
      <h6 class="mb-1">
        <i class="fas fa-user me-2"></i>
        {{ data.memberName }}
      </h6>
      <small class="text-muted">{{ deleteOptions.phoneNumber }}</small>
    </div>

    <!-- Warning Message -->
    <div *ngIf="deleteOptions.hasMultipleMemberships" class="alert alert-warning mb-3">
      <i class="fas fa-exclamation-triangle me-2"></i>
      {{ deleteOptions.warningMessage }}
    </div>

    <!-- Membership Selection -->
    <div class="membership-list">
      <h6 class="mb-3">
        <i class="fas fa-list me-2"></i>
        Silinebilir Üyelikler ({{ deleteOptions.deletableMemberships.length }})
      </h6>

      <div class="row">
        <div 
          *ngFor="let membership of deleteOptions.deletableMemberships" 
          class="col-12 mb-3"
        >
          <div 
            class="card membership-card"
            [class.selected]="selectedMembership?.membershipID === membership.membershipID"
            [class.border-success]="selectedMembership?.membershipID === membership.membershipID"
            (click)="selectMembership(membership)"
            style="cursor: pointer;"
          >
            <div class="card-body">
              <!-- Header -->
              <div class="d-flex justify-content-between align-items-start mb-2">
                <h6 class="card-title mb-0">
                  <span class="badge bg-primary me-2">{{ membership.branch }}</span>
                  {{ membership.typeName }}
                </h6>
                <div class="form-check">
                  <input 
                    class="form-check-input" 
                    type="radio" 
                    [checked]="selectedMembership?.membershipID === membership.membershipID"
                    readonly
                  >
                </div>
              </div>

              <!-- Membership Details -->
              <div class="row text-sm">
                <div class="col-6">
                  <small class="text-muted">Başlangıç:</small><br>
                  <span>{{ formatDate(membership.startDate) }}</span>
                </div>
                <div class="col-6">
                  <small class="text-muted">Bitiş:</small><br>
                  <span>{{ formatDate(membership.endDate) }}</span>
                </div>
              </div>

              <div class="row text-sm mt-2">
                <div class="col-6">
                  <small class="text-muted">Kalan Gün:</small><br>
                  <span [class]="getRemainingDaysColor(membership.remainingDays)">
                    <strong>{{ membership.remainingDays }} gün</strong>
                  </span>
                </div>
                <div class="col-6">
                  <small class="text-muted">Ödenen Tutar:</small><br>
                  <span class="text-success">
                    <strong>{{ formatCurrency(membership.paidAmount) }}</strong>
                  </span>
                </div>
              </div>

              <!-- Payment Info -->
              <div class="mt-2">
                <small class="text-muted">Ödeme Yöntemi:</small>
                <span class="badge bg-secondary ms-1">{{ membership.paymentMethod }}</span>
                <small class="text-muted ms-2">{{ formatDate(membership.paymentDate) }}</small>
              </div>

              <!-- Refund Info -->
              <div *ngIf="membership.possibleRefundAmount > 0" class="mt-2">
                <div class="alert alert-warning py-2 mb-0">
                  <small>
                    <i class="fas fa-money-bill-wave me-1"></i>
                    Tahmini İade: <strong>{{ formatCurrency(membership.possibleRefundAmount) }}</strong>
                  </small>
                </div>
              </div>

              <!-- Warning -->
              <div class="mt-2">
                <small class="text-danger">
                  <i class="fas fa-exclamation-circle me-1"></i>
                  {{ membership.deleteWarning }}
                </small>
              </div>

              <!-- Status Badges -->
              <div class="mt-2">
                <span *ngIf="membership.isFrozen" class="badge bg-info me-1">
                  <i class="fas fa-snowflake me-1"></i>Dondurulmuş
                </span>
                <span *ngIf="membership.isFutureStartDate" class="badge bg-warning me-1">
                  <i class="fas fa-clock me-1"></i>Gelecek Tarihli
                </span>
                <span *ngIf="!membership.canBeDeleted" class="badge bg-danger">
                  <i class="fas fa-ban me-1"></i>Silinemez
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Total Refund -->
    <div *ngIf="deleteOptions.totalRefundAmount > 0" class="alert alert-info mt-3">
      <strong>
        <i class="fas fa-calculator me-2"></i>
        Toplam Tahmini İade: {{ formatCurrency(deleteOptions.totalRefundAmount) }}
      </strong>
    </div>
  </div>
</div>

<div class="modal-footer">
  <button 
    type="button" 
    class="btn btn-secondary" 
    (click)="cancel()"
    [disabled]="isDeleting"
  >
    <i class="fas fa-times me-2"></i>İptal
  </button>
  
  <button 
    type="button" 
    class="btn btn-danger" 
    (click)="confirmDelete()"
    [disabled]="!selectedMembership || isDeleting || !selectedMembership.canBeDeleted"
  >
    <span *ngIf="isDeleting" class="spinner-border spinner-border-sm me-2"></span>
    <i *ngIf="!isDeleting" class="fas fa-trash-alt me-2"></i>
    {{ isDeleting ? 'Siliniyor...' : 'Seçili Üyeliği Sil' }}
  </button>
</div>
