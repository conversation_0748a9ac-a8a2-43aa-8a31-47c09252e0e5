﻿using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUserCompanyDal: EfEntityRepositoryBase<UserCompany, GymContext>, IUserCompanyDal
    {
        public List<UserCompanyDetailDto> GetUserCompanyDetails()
        {
            using (var context = new GymContext())
            {
                var result = from uc in context.UserCompanies
                             join cu in context.CompanyUsers on uc.UserID equals cu.CompanyUserID
                             join c in context.Companies on uc.CompanyId equals c.CompanyID
                             where uc.IsActive == true
                             select new UserCompanyDetailDto
                             {
                                 UserCompanyId = uc.UserCompanyID,
                                 CompanyUserName = cu.Name,
                                 CompanyName = c.CompanyName,
                                 isActive=uc.IsActive,
                             };
                return result.ToList();
            }
        }

        public int GetUserCompanyId(int userId)
        {
            using (var context = new GymContext())
            {
                var userCompany = context.UserCompanies
                    .FirstOrDefault(uc => uc.UserID == userId && uc.IsActive == true);
                
                return userCompany?.CompanyId ?? -1;
            }
        }
    }
}
