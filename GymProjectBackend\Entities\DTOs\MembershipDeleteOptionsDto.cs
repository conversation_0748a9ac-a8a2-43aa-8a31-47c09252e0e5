using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class MembershipDeleteOptionsDto : IDto
    {
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public string PhoneNumber { get; set; }
        public List<DeletableMembershipDto> DeletableMemberships { get; set; }
        public bool HasMultipleMemberships { get; set; }
        public string WarningMessage { get; set; }
        public decimal TotalRefundAmount { get; set; }
    }

    public class DeletableMembershipDto : IDto
    {
        public int MembershipID { get; set; }
        public int MembershipTypeID { get; set; }
        public string Branch { get; set; }
        public string TypeName { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int RemainingDays { get; set; }
        public bool IsActive { get; set; }
        public bool IsFrozen { get; set; }
        public decimal PaidAmount { get; set; }
        public string PaymentMethod { get; set; }
        public DateTime PaymentDate { get; set; }
        public decimal PossibleRefundAmount { get; set; }
        public string DeleteWarning { get; set; }
        public bool CanBeDeleted { get; set; }
        public string ReasonCannotDelete { get; set; }
    }
}
