﻿using Business.Abstract;
using Entities.Concrete;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class LicenseTransactionsController : ControllerBase
    {
        private readonly ILicenseTransactionService _licenseTransactionService;

        public LicenseTransactionsController(ILicenseTransactionService licenseTransactionService)
        {
            _licenseTransactionService = licenseTransactionService;
        }

        [HttpGet("getall")]
        public IActionResult GetAll()
        {
            var result = _licenseTransactionService.GetAll();
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpGet("getbyuserid")]
        public IActionResult GetByUserId(int userId)
        {
            var result = _licenseTransactionService.GetByUserId(userId);
            return result.Success ? Ok(result) : BadRequest(result);
        }

        [HttpPost("add")]
        public IActionResult Add(LicenseTransaction licenseTransaction)
        {
            var result = _licenseTransactionService.Add(licenseTransaction);
            return result.Success ? Ok(result) : BadRequest(result);
        }
    }
}