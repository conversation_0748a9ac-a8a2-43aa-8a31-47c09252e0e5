export interface MembershipDeleteOptions {
  memberID: number;
  memberName: string;
  phoneNumber: string;
  deletableMemberships: DeletableMembership[];
  hasMultipleMemberships: boolean;
  warningMessage: string;
  totalRefundAmount: number;
}

export interface DeletableMembership {
  membershipID: number;
  membershipTypeID: number;
  branch: string;
  typeName: string;
  startDate: Date;
  endDate: Date;
  remainingDays: number;
  isActive: boolean;
  isFrozen: boolean;
  isFutureStartDate: boolean;
  paidAmount: number;
  paymentMethod: string;
  paymentDate: Date;
  possibleRefundAmount: number;
  deleteWarning: string;
  canBeDeleted: boolean;
  reasonCannotDelete: string;
}
