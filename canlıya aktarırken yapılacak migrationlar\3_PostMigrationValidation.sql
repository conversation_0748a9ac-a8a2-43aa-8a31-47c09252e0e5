-- =============================================
-- MİGRATİON SONRASI DOĞRULAMA SCRİPTİ
-- =============================================

USE [GymProject]
GO

PRINT '============================================='
PRINT 'ÇOKLU BRANŞ ÜYELİK SİSTEMİ - DOĞRULAMA'
PRINT '============================================='

-- =============================================
-- 1. OLUŞTURULAN İNDEXLERİ KONTROL ET
-- =============================================

PRINT 'Oluşturulan indexler kontrol ediliyor...'

DECLARE @CreatedIndexes TABLE (IndexName NVARCHAR(255), TableName NVARCHAR(255), Status NVARCHAR(50))

-- Kritik indexleri kontrol et
INSERT INTO @CreatedIndexes
SELECT 
    i.name as IndexName,
    t.name as TableName,
    CASE WHEN i.name IS NOT NULL THEN 'OLUŞTURULDU' ELSE 'EKSİK' END as Status
FROM sys.tables t
LEFT JOIN sys.indexes i ON t.object_id = i.object_id 
WHERE t.name IN ('Memberships', 'Payments', 'Members', 'MembershipTypes', 'EntryExitHistories')
    AND i.name IN (
        'IX_Memberships_CompanyID_MemberID_IsActive_EndDate',
        'IX_Memberships_CompanyID_MembershipTypeID_IsActive',
        'IX_Memberships_MemberID_MembershipTypeID_Active_EndDate',
        'IX_Payments_CompanyID_MemberShipID_IsActive',
        'IX_Payments_CompanyID_PaymentDate_IsActive',
        'IX_Members_CompanyID_IsActive_Name_Phone',
        'IX_MembershipTypes_CompanyID_Branch_IsActive',
        'IX_EntryExitHistories_CompanyID_EntryDate'
    )

SELECT * FROM @CreatedIndexes ORDER BY TableName, IndexName

DECLARE @IndexCount INT
SELECT @IndexCount = COUNT(*) FROM @CreatedIndexes WHERE Status = 'OLUŞTURULDU'
PRINT CONCAT('Toplam oluşturulan index sayısı: ', @IndexCount)

-- =============================================
-- 2. YENİ ALANLARI KONTROL ET
-- =============================================

PRINT ''
PRINT 'Yeni alanlar kontrol ediliyor...'

-- RenewalType alanı
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Memberships') AND name = 'RenewalType')
    PRINT '✓ Memberships.RenewalType alanı eklendi'
ELSE
    PRINT '⚠ Memberships.RenewalType alanı eklenemedi'

-- OriginalMembershipID alanı
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Memberships') AND name = 'OriginalMembershipID')
    PRINT '✓ Memberships.OriginalMembershipID alanı eklendi'
ELSE
    PRINT '⚠ Memberships.OriginalMembershipID alanı eklenemedi'

-- Computed columns
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Memberships') AND name = 'RemainingDaysComputed')
    PRINT '✓ Memberships.RemainingDaysComputed computed column eklendi'
ELSE
    PRINT '⚠ Memberships.RemainingDaysComputed computed column eklenemedi'

IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Memberships') AND name = 'IsExpired')
    PRINT '✓ Memberships.IsExpired computed column eklendi'
ELSE
    PRINT '⚠ Memberships.IsExpired computed column eklenemedi'

-- =============================================
-- 3. VIEW VE STORED PROCEDURE KONTROL
-- =============================================

PRINT ''
PRINT 'View ve stored procedure kontrol ediliyor...'

-- Multi-branch view
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_MultiBranchMemberships')
    PRINT '✓ vw_MultiBranchMemberships view oluşturuldu'
ELSE
    PRINT '⚠ vw_MultiBranchMemberships view oluşturulamadı'

-- Stored procedures
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_AnalyzeMembershipRenewal')
    PRINT '✓ sp_AnalyzeMembershipRenewal stored procedure oluşturuldu'
ELSE
    PRINT '⚠ sp_AnalyzeMembershipRenewal stored procedure oluşturulamadı'

IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetMembershipDeleteOptions')
    PRINT '✓ sp_GetMembershipDeleteOptions stored procedure oluşturuldu'
ELSE
    PRINT '⚠ sp_GetMembershipDeleteOptions stored procedure oluşturulamadı'

-- =============================================
-- 4. PERFORMANS TESTİ
-- =============================================

PRINT ''
PRINT 'Performans testi yapılıyor...'

-- Test 1: Aktif üyelik sorgusu
DECLARE @StartTime1 DATETIME2 = GETDATE()

SELECT COUNT(*) as ActiveMembershipCount
FROM Memberships ms
INNER JOIN Members m ON ms.MemberID = m.MemberID
INNER JOIN MembershipTypes mt ON ms.MembershipTypeID = mt.MembershipTypeID
WHERE ms.IsActive = 1 
    AND ms.EndDate > GETDATE()
    AND ms.IsFrozen = 0

DECLARE @EndTime1 DATETIME2 = GETDATE()
DECLARE @Duration1 INT = DATEDIFF(MILLISECOND, @StartTime1, @EndTime1)
PRINT CONCAT('Aktif üyelik sorgusu süresi: ', @Duration1, ' ms')

-- Test 2: Çoklu branş sorgusu
DECLARE @StartTime2 DATETIME2 = GETDATE()

SELECT 
    m.MemberID,
    m.Name,
    COUNT(ms.MembershipID) AS TotalActiveMemberships,
    COUNT(DISTINCT mt.Branch) AS UniqueBranches
FROM Members m
INNER JOIN Memberships ms ON m.MemberID = ms.MemberID
INNER JOIN MembershipTypes mt ON ms.MembershipTypeID = mt.MembershipTypeID
WHERE ms.IsActive = 1 
    AND ms.EndDate > GETDATE()
    AND ms.IsFrozen = 0
    AND m.IsActive = 1
GROUP BY m.MemberID, m.Name
HAVING COUNT(DISTINCT mt.Branch) > 1

DECLARE @EndTime2 DATETIME2 = GETDATE()
DECLARE @Duration2 INT = DATEDIFF(MILLISECOND, @StartTime2, @EndTime2)
PRINT CONCAT('Çoklu branş sorgusu süresi: ', @Duration2, ' ms')

-- Test 3: Ödeme geçmişi sorgusu
DECLARE @StartTime3 DATETIME2 = GETDATE()

SELECT TOP 100
    p.PaymentID,
    p.PaymentDate,
    p.PaymentAmount,
    m.Name,
    mt.Branch
FROM Payments p
INNER JOIN Memberships ms ON p.MemberShipID = ms.MembershipID
INNER JOIN Members m ON ms.MemberID = m.MemberID
INNER JOIN MembershipTypes mt ON ms.MembershipTypeID = mt.MembershipTypeID
WHERE p.IsActive = 1
ORDER BY p.PaymentDate DESC

DECLARE @EndTime3 DATETIME2 = GETDATE()
DECLARE @Duration3 INT = DATEDIFF(MILLISECOND, @StartTime3, @EndTime3)
PRINT CONCAT('Ödeme geçmişi sorgusu süresi: ', @Duration3, ' ms')

-- =============================================
-- 5. VERİ TUTARLILIĞI KONTROL
-- =============================================

PRINT ''
PRINT 'Veri tutarlılığı kontrol ediliyor...'

-- Çoklu branş istatistikleri
DECLARE @TotalMembers INT, @MultiBranchMembers INT
SELECT @TotalMembers = COUNT(DISTINCT MemberID) FROM Memberships WHERE IsActive = 1 AND EndDate > GETDATE()

SELECT @MultiBranchMembers = COUNT(*)
FROM (
    SELECT MemberID
    FROM Memberships ms
    INNER JOIN MembershipTypes mt ON ms.MembershipTypeID = mt.MembershipTypeID
    WHERE ms.IsActive = 1 AND ms.EndDate > GETDATE() AND ms.IsFrozen = 0
    GROUP BY MemberID
    HAVING COUNT(DISTINCT mt.Branch) > 1
) MultiBranch

PRINT CONCAT('Toplam aktif üye sayısı: ', @TotalMembers)
PRINT CONCAT('Çoklu branş üyeliği olan üye sayısı: ', @MultiBranchMembers)
PRINT CONCAT('Çoklu branş oranı: %', ROUND(CAST(@MultiBranchMembers AS FLOAT) / @TotalMembers * 100, 2))

-- =============================================
-- 6. SONUÇ RAPORU
-- =============================================

PRINT ''
PRINT '============================================='
PRINT 'MİGRATİON SONUÇ RAPORU'
PRINT '============================================='

-- Genel durum
IF @IndexCount >= 8
    PRINT '✓ Index oluşturma: BAŞARILI'
ELSE
    PRINT '⚠ Index oluşturma: KISMEN BAŞARILI'

-- Performans değerlendirmesi
IF @Duration1 < 1000 AND @Duration2 < 2000 AND @Duration3 < 1000
    PRINT '✓ Performans: MÜKEMMEL'
ELSE IF @Duration1 < 3000 AND @Duration2 < 5000 AND @Duration3 < 3000
    PRINT '✓ Performans: İYİ'
ELSE
    PRINT '⚠ Performans: ORTA (İyileştirme gerekebilir)'

PRINT ''
PRINT 'Öneriler:'
PRINT '- Uygulama sunucusunu yeniden başlatın'
PRINT '- Cache temizleyin'
PRINT '- Yeni API endpoint''lerini test edin'
PRINT '- Frontend component''lerini test edin'
PRINT ''
PRINT '============================================='
PRINT 'DOĞRULAMA TAMAMLANDI'
PRINT '============================================='
GO
