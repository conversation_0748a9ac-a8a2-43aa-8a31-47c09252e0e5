.membership-card {
  transition: all 0.3s ease;
  border: 2px solid #dee2e6;
}

.membership-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-color: #007bff;
}

.membership-card.selected {
  border-color: #28a745 !important;
  background-color: #f8fff9;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);
}

.membership-card.selected .card-body {
  position: relative;
}

.membership-card.selected::before {
  content: '';
  position: absolute;
  top: 10px;
  right: 10px;
  width: 20px;
  height: 20px;
  background-color: #28a745;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.membership-card.selected::after {
  content: '✓';
  position: absolute;
  top: 15px;
  right: 15px;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.text-sm {
  font-size: 0.875rem;
}

.modal-header {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  border-bottom: none;
}

.modal-header .btn-close {
  filter: invert(1);
}

.modal-body {
  max-height: 70vh;
  overflow-y: auto;
}

.membership-list {
  max-height: 400px;
  overflow-y: auto;
}

.badge {
  font-size: 0.75rem;
}

.alert {
  border-radius: 8px;
}

.card {
  border-radius: 10px;
}

.form-check-input:checked {
  background-color: #28a745;
  border-color: #28a745;
}

/* Responsive */
@media (max-width: 768px) {
  .modal-body {
    max-height: 60vh;
  }
  
  .membership-list {
    max-height: 300px;
  }
  
  .row .col-6 {
    margin-bottom: 0.5rem;
  }
}

/* Animation for loading */
.spinner-border {
  width: 2rem;
  height: 2rem;
}

/* Custom scrollbar */
.membership-list::-webkit-scrollbar {
  width: 6px;
}

.membership-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.membership-list::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

.membership-list::-webkit-scrollbar-thumb:hover {
  background: #555;
}
