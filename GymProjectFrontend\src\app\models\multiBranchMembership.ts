export interface MultiBranchMembership {
  memberID: number;
  memberName: string;
  phoneNumber: string;
  gender: number;
  activeMemberships: ActiveMembershipDetail[];
  combinedBranchDisplay: string; // "Fitness(40), Crossfit(60)"
  totalActiveMemberships: number;
  earliestEndDate?: Date;
  latestEndDate?: Date;
  hasMultipleBranches: boolean;
}

export interface ActiveMembershipDetail {
  membershipID: number;
  membershipTypeID: number;
  branch: string;
  typeName: string;
  startDate: Date;
  endDate: Date;
  remainingDays: number;
  isActive: boolean;
  isFrozen: boolean;
  isFutureStartDate: boolean;
  updatedDate?: Date;
  lastPaymentAmount: number;
  lastPaymentMethod: string;
}
