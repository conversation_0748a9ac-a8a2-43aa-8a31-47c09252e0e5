using Core.DataAccess;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IExpenseDal : IEntityRepository<Expense>
    {
        // Expense'e özel DAL metotları gerekirse buraya eklenebilir.
        // <PERSON><PERSON><PERSON>in, belirli bir tarihe göre giderleri getirme, DTO döndürme vb.
    }
}