﻿using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfMembershipDal : EfCompanyEntityRepositoryBase<Membership, GymContext>, IMembershipDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        public EfMembershipDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext) : base(companyContext)
        {
            _companyContext = companyContext;
        }
        public MembershipType GetMembershipType(int membershipTypeId)
        {
            using (GymContext context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                return context.MembershipTypes.SingleOrDefault(mt => mt.MembershipTypeID == membershipTypeId && mt.CompanyID == companyId);
            }
        }
        public void CancelFreeze(int membershipId)
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var membership = context.Memberships.SingleOrDefault(m => m.MembershipID == membershipId && m.CompanyID == companyId);
                if (membership != null && membership.IsFrozen)
                {
                    membership.IsFrozen = false;
                    membership.EndDate = membership.OriginalEndDate ?? membership.EndDate;
                    membership.FreezeStartDate = null;
                    membership.FreezeEndDate = null;
                    membership.FreezeDays = 0;
                    membership.OriginalEndDate = null;
                    membership.UpdatedDate = DateTime.Now; // UpdatedDate alanını güncelle
                    context.SaveChanges();
                }
            }
        }

        public void ReactivateFromToday(int membershipId)
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var membership = context.Memberships.SingleOrDefault(m => m.MembershipID == membershipId && m.CompanyID == companyId);
                if (membership != null && membership.IsFrozen)
                {
                    var today = DateTime.Now.Date;
                    var usedFreezeDays = (today - membership.FreezeStartDate.Value.Date).Days;
                    var unusedFreezeDays = membership.FreezeDays - usedFreezeDays;

                    membership.IsFrozen = false;
                    membership.EndDate = membership.OriginalEndDate?.AddDays(usedFreezeDays) ?? membership.EndDate;
                    membership.FreezeStartDate = null;
                    membership.FreezeEndDate = null;
                    membership.FreezeDays = 0;
                    membership.OriginalEndDate = null;
                    membership.UpdatedDate = DateTime.Now; // UpdatedDate alanını güncelle
                    context.SaveChanges();
                }
            }
        }


        public List<MembershipFreezeDto> GetFrozenMemberships()
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var frozenMemberships = from m in context.Memberships
                                        join mem in context.Members on m.MemberID equals mem.MemberID
                                        join mt in context.MembershipTypes on m.MembershipTypeID equals mt.MembershipTypeID
                                        where m.IsFrozen && m.IsActive == true
                                        && m.CompanyID == companyId // Şirket ID'sine göre filtrele
                                        && mem.CompanyID == companyId // Üyelerin de aynı şirkete ait olduğundan emin ol
                                        && mt.CompanyID == companyId // Üyelik türlerinin de aynı şirkete ait olduğundan emin ol
                                        select new MembershipFreezeDto
                                        {
                                            MembershipID = m.MembershipID,
                                            MemberName = mem.Name,
                                            PhoneNumber = mem.PhoneNumber,
                                            StartDate = m.StartDate,
                                            EndDate = m.EndDate,
                                            FreezeStartDate = m.FreezeStartDate.Value,
                                            FreezeEndDate = m.FreezeEndDate.Value,
                                            FreezeDays = (int)m.FreezeDays,
                                            Branch = mt.Branch
                                        };

                return frozenMemberships.ToList();
            }
        }

        public void FreezeMembership(int membershipId, int freezeDays)
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var membership = context.Memberships.SingleOrDefault(m => m.MembershipID == membershipId && m.CompanyID == companyId);
                if (membership != null)
                {
                    membership.IsFrozen = true;
                    membership.FreezeStartDate = DateTime.Now;
                    membership.FreezeEndDate = DateTime.Now.AddDays(freezeDays).Date.AddHours(0).AddMinutes(1);
                    membership.FreezeDays = freezeDays;
                    membership.OriginalEndDate = membership.EndDate;
                    membership.EndDate = membership.EndDate.AddDays(freezeDays);
                    membership.UpdatedDate = DateTime.Now; // UpdatedDate alanını güncelle
                    context.SaveChanges();
                }
            }
        }


        public void UnfreezeMembership(int membershipId)
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var membership = context.Memberships.SingleOrDefault(m => m.MembershipID == membershipId && m.CompanyID == companyId);
                if (membership != null)
                {
                    var remainingFreezeDays = (membership.FreezeEndDate?.Date - DateTime.Now.Date)?.Days ?? 0;
                    if (remainingFreezeDays < 0) remainingFreezeDays = 0;

                    membership.IsFrozen = false;
                    membership.EndDate = membership.EndDate.AddDays(-remainingFreezeDays);
                    membership.FreezeStartDate = null;
                    membership.FreezeEndDate = null;
                    membership.FreezeDays = 0;
                    membership.OriginalEndDate = null;
                    membership.UpdatedDate = DateTime.Now; // UpdatedDate alanını güncelle
                    context.SaveChanges();
                }
            }
        }

        public bool IsMembershipFrozen(int membershipId)
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                return context.Memberships
                    .Where(m => m.MembershipID == membershipId && m.CompanyID == companyId)
                    .Select(m => m.IsFrozen==true)
                    .FirstOrDefault();
            }
        }

        public int GetRemainingFreezeDays(int membershipId)
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var membership = context.Memberships.SingleOrDefault(m => m.MembershipID == membershipId && m.CompanyID == companyId);
                if (membership?.IsFrozen == true && membership.FreezeEndDate.HasValue)
                {
                    var remainingDays = (membership.FreezeEndDate.Value.Date - DateTime.Now.Date).Days;
                    return remainingDays > 0 ? remainingDays : 0;
                }
                return 0;
            }
        }

        // Çoklu branş ve akıllı yenileme için yeni metodlar
        public List<ActiveMembershipDetailDto> GetActiveMembershipsByMember(int memberId)
        {
            using (var context = new GymContext())
            {
                int companyId = _companyContext.GetCompanyId();

                var activeMemberships = from m in context.Memberships
                                       join mt in context.MembershipTypes on m.MembershipTypeID equals mt.MembershipTypeID
                                       join p in context.Payments on m.MembershipID equals p.MemberShipID into payments
                                       from p in payments.OrderByDescending(x => x.PaymentDate).Take(1).DefaultIfEmpty()
                                       where m.MemberID == memberId
                                       && m.IsActive == true
                                       && m.EndDate >= DateTime.Now
                                       && m.CompanyID == companyId
                                       && mt.CompanyID == companyId
                                       select new ActiveMembershipDetailDto
                                       {
                                           MembershipID = m.MembershipID,
                                           MembershipTypeID = m.MembershipTypeID,
                                           Branch = mt.Branch,
                                           TypeName = mt.TypeName,
                                           StartDate = m.StartDate,
                                           EndDate = m.EndDate,
                                           RemainingDays = (int)Math.Ceiling((m.EndDate - DateTime.Now).TotalDays),
                                           IsActive = m.IsActive ?? false,
                                           IsFrozen = m.IsFrozen,
                                           IsFutureStartDate = m.StartDate > DateTime.Now,
                                           UpdatedDate = m.UpdatedDate,
                                           LastPaymentAmount = p != null ? p.PaymentAmount : 0,
                                           LastPaymentMethod = p != null ? p.PaymentMethod : ""
                                       };

                return activeMemberships.ToList();
            }
        }

        public MultiBranchMembershipDto GetMemberMultiBranchMemberships(int memberId)
        {
            using (var context = new GymContext())
            {
                int companyId = _companyContext.GetCompanyId();

                var member = context.Members.FirstOrDefault(m => m.MemberID == memberId && m.CompanyID == companyId);
                if (member == null) return null;

                var activeMemberships = GetActiveMembershipsByMember(memberId);

                var result = new MultiBranchMembershipDto
                {
                    MemberID = memberId,
                    MemberName = member.Name,
                    PhoneNumber = member.PhoneNumber,
                    Gender = member.Gender,
                    ActiveMemberships = activeMemberships,
                    TotalActiveMemberships = activeMemberships.Count,
                    HasMultipleBranches = activeMemberships.Select(m => m.Branch).Distinct().Count() > 1,
                    EarliestEndDate = activeMemberships.Any() ? activeMemberships.Min(m => m.EndDate) : (DateTime?)null,
                    LatestEndDate = activeMemberships.Any() ? activeMemberships.Max(m => m.EndDate) : (DateTime?)null
                };

                // Birleşik branş gösterimi oluştur: "Fitness(40), Crossfit(60)"
                if (activeMemberships.Any())
                {
                    var branchDisplays = activeMemberships
                        .GroupBy(m => m.Branch)
                        .Select(g => $"{g.Key}({g.Max(x => x.RemainingDays)})")
                        .ToList();

                    result.CombinedBranchDisplay = string.Join(", ", branchDisplays);
                    result.AllBranches = string.Join(", ", activeMemberships.Select(m => m.Branch).Distinct());
                }

                return result;
            }
        }

        public List<DeletableMembershipDto> GetDeletableMembershipsByMember(int memberId)
        {
            using (var context = new GymContext())
            {
                int companyId = _companyContext.GetCompanyId();

                var deletableMemberships = from m in context.Memberships
                                          join mt in context.MembershipTypes on m.MembershipTypeID equals mt.MembershipTypeID
                                          join p in context.Payments on m.MembershipID equals p.MemberShipID into payments
                                          from p in payments.OrderByDescending(x => x.PaymentDate).Take(1).DefaultIfEmpty()
                                          where m.MemberID == memberId
                                          && m.IsActive == true
                                          && m.CompanyID == companyId
                                          && mt.CompanyID == companyId
                                          select new DeletableMembershipDto
                                          {
                                              MembershipID = m.MembershipID,
                                              MembershipTypeID = m.MembershipTypeID,
                                              Branch = mt.Branch,
                                              TypeName = mt.TypeName,
                                              StartDate = m.StartDate,
                                              EndDate = m.EndDate,
                                              RemainingDays = (int)Math.Ceiling((m.EndDate - DateTime.Now).TotalDays),
                                              IsActive = m.IsActive ?? false,
                                              IsFrozen = m.IsFrozen,
                                              PaidAmount = p != null ? p.PaymentAmount : 0,
                                              PaymentMethod = p != null ? p.PaymentMethod : "",
                                              PaymentDate = p != null ? p.PaymentDate : DateTime.MinValue,
                                              PossibleRefundAmount = CalculateRefundAmount(m.EndDate, p != null ? p.PaymentAmount : 0),
                                              CanBeDeleted = true,
                                              DeleteWarning = GenerateDeleteWarning(m.EndDate, mt.Branch, mt.TypeName)
                                          };

                return deletableMemberships.ToList();
            }
        }

        public bool HasActiveMembershipForBranchAndType(int memberId, int membershipTypeId)
        {
            using (var context = new GymContext())
            {
                int companyId = _companyContext.GetCompanyId();

                return context.Memberships.Any(m =>
                    m.MemberID == memberId
                    && m.MembershipTypeID == membershipTypeId
                    && m.IsActive == true
                    && m.EndDate >= DateTime.Now
                    && m.CompanyID == companyId);
            }
        }

        public Membership GetActiveMembershipForBranchAndType(int memberId, int membershipTypeId)
        {
            using (var context = new GymContext())
            {
                int companyId = _companyContext.GetCompanyId();

                return context.Memberships.FirstOrDefault(m =>
                    m.MemberID == memberId
                    && m.MembershipTypeID == membershipTypeId
                    && m.IsActive == true
                    && m.EndDate >= DateTime.Now
                    && m.CompanyID == companyId);
            }
        }

        public int GetActiveMembershipCount(int memberId)
        {
            using (var context = new GymContext())
            {
                int companyId = _companyContext.GetCompanyId();

                return context.Memberships.Count(m =>
                    m.MemberID == memberId
                    && m.IsActive == true
                    && m.EndDate >= DateTime.Now
                    && m.CompanyID == companyId);
            }
        }

        public bool HasMultipleMemberships(int memberId)
        {
            return GetActiveMembershipCount(memberId) > 1;
        }

        // Yardımcı metodlar
        private decimal CalculateRefundAmount(DateTime endDate, decimal paidAmount)
        {
            var remainingDays = (endDate - DateTime.Now).TotalDays;
            if (remainingDays <= 0) return 0;

            // Basit hesaplama: Kalan gün oranında iade
            var totalDays = (endDate - DateTime.Now.AddDays(-30)).TotalDays; // Yaklaşık 30 günlük paket varsayımı
            if (totalDays <= 0) return 0;

            var refundRatio = remainingDays / totalDays;
            return (decimal)(paidAmount * refundRatio);
        }

        private string GenerateDeleteWarning(DateTime endDate, string branch, string typeName)
        {
            var remainingDays = (int)Math.Ceiling((endDate - DateTime.Now).TotalDays);

            if (remainingDays > 0)
            {
                return $"{branch} - {typeName} üyeliğinin {remainingDays} günü kalmıştır. Silme işlemi geri alınamaz.";
            }
            else
            {
                return $"{branch} - {typeName} üyeliği süresi dolmuştur.";
            }
        }
    }
}
