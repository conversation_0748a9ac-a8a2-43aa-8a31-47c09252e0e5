﻿using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfMembershipDal : EfCompanyEntityRepositoryBase<Membership, GymContext>, IMembershipDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        public EfMembershipDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext) : base(companyContext)
        {
            _companyContext = companyContext;
        }
        public MembershipType GetMembershipType(int membershipTypeId)
        {
            using (GymContext context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                return context.MembershipTypes.SingleOrDefault(mt => mt.MembershipTypeID == membershipTypeId && mt.CompanyID == companyId);
            }
        }
        public void CancelFreeze(int membershipId)
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var membership = context.Memberships.SingleOrDefault(m => m.MembershipID == membershipId && m.CompanyID == companyId);
                if (membership != null && membership.IsFrozen)
                {
                    membership.IsFrozen = false;
                    membership.EndDate = membership.OriginalEndDate ?? membership.EndDate;
                    membership.FreezeStartDate = null;
                    membership.FreezeEndDate = null;
                    membership.FreezeDays = 0;
                    membership.OriginalEndDate = null;
                    membership.UpdatedDate = DateTime.Now; // UpdatedDate alanını güncelle
                    context.SaveChanges();
                }
            }
        }

        public void ReactivateFromToday(int membershipId)
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var membership = context.Memberships.SingleOrDefault(m => m.MembershipID == membershipId && m.CompanyID == companyId);
                if (membership != null && membership.IsFrozen)
                {
                    var today = DateTime.Now.Date;
                    var usedFreezeDays = (today - membership.FreezeStartDate.Value.Date).Days;
                    var unusedFreezeDays = membership.FreezeDays - usedFreezeDays;

                    membership.IsFrozen = false;
                    membership.EndDate = membership.OriginalEndDate?.AddDays(usedFreezeDays) ?? membership.EndDate;
                    membership.FreezeStartDate = null;
                    membership.FreezeEndDate = null;
                    membership.FreezeDays = 0;
                    membership.OriginalEndDate = null;
                    membership.UpdatedDate = DateTime.Now; // UpdatedDate alanını güncelle
                    context.SaveChanges();
                }
            }
        }


        public List<MembershipFreezeDto> GetFrozenMemberships()
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var frozenMemberships = from m in context.Memberships
                                        join mem in context.Members on m.MemberID equals mem.MemberID
                                        join mt in context.MembershipTypes on m.MembershipTypeID equals mt.MembershipTypeID
                                        where m.IsFrozen && m.IsActive == true
                                        && m.CompanyID == companyId // Şirket ID'sine göre filtrele
                                        && mem.CompanyID == companyId // Üyelerin de aynı şirkete ait olduğundan emin ol
                                        && mt.CompanyID == companyId // Üyelik türlerinin de aynı şirkete ait olduğundan emin ol
                                        select new MembershipFreezeDto
                                        {
                                            MembershipID = m.MembershipID,
                                            MemberName = mem.Name,
                                            PhoneNumber = mem.PhoneNumber,
                                            StartDate = m.StartDate,
                                            EndDate = m.EndDate,
                                            FreezeStartDate = m.FreezeStartDate.Value,
                                            FreezeEndDate = m.FreezeEndDate.Value,
                                            FreezeDays = (int)m.FreezeDays,
                                            Branch = mt.Branch
                                        };

                return frozenMemberships.ToList();
            }
        }

        public void FreezeMembership(int membershipId, int freezeDays)
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var membership = context.Memberships.SingleOrDefault(m => m.MembershipID == membershipId && m.CompanyID == companyId);
                if (membership != null)
                {
                    membership.IsFrozen = true;
                    membership.FreezeStartDate = DateTime.Now;
                    membership.FreezeEndDate = DateTime.Now.AddDays(freezeDays).Date.AddHours(0).AddMinutes(1);
                    membership.FreezeDays = freezeDays;
                    membership.OriginalEndDate = membership.EndDate;
                    membership.EndDate = membership.EndDate.AddDays(freezeDays);
                    membership.UpdatedDate = DateTime.Now; // UpdatedDate alanını güncelle
                    context.SaveChanges();
                }
            }
        }


        public void UnfreezeMembership(int membershipId)
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var membership = context.Memberships.SingleOrDefault(m => m.MembershipID == membershipId && m.CompanyID == companyId);
                if (membership != null)
                {
                    var remainingFreezeDays = (membership.FreezeEndDate?.Date - DateTime.Now.Date)?.Days ?? 0;
                    if (remainingFreezeDays < 0) remainingFreezeDays = 0;

                    membership.IsFrozen = false;
                    membership.EndDate = membership.EndDate.AddDays(-remainingFreezeDays);
                    membership.FreezeStartDate = null;
                    membership.FreezeEndDate = null;
                    membership.FreezeDays = 0;
                    membership.OriginalEndDate = null;
                    membership.UpdatedDate = DateTime.Now; // UpdatedDate alanını güncelle
                    context.SaveChanges();
                }
            }
        }

        public bool IsMembershipFrozen(int membershipId)
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                return context.Memberships
                    .Where(m => m.MembershipID == membershipId && m.CompanyID == companyId)
                    .Select(m => m.IsFrozen==true)
                    .FirstOrDefault();
            }
        }

        public int GetRemainingFreezeDays(int membershipId)
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var membership = context.Memberships.SingleOrDefault(m => m.MembershipID == membershipId && m.CompanyID == companyId);
                if (membership?.IsFrozen == true && membership.FreezeEndDate.HasValue)
                {
                    var remainingDays = (membership.FreezeEndDate.Value.Date - DateTime.Now.Date).Days;
                    return remainingDays > 0 ? remainingDays : 0;
                }
                return 0;
            }
        }
    }
}
