export interface MemberFilter {
    memberID: number;
    membershipID: number;
    membershipTypeID: number;
    name: string;
    gender:number;
    phoneNumber:string;
    typeName:string;
    branch:string;
    startDate:string;
    endDate:string;
    remainingDays:number;
    isFutureStartDate: boolean; // Üyeliğin başlangıç tarihi gelecekte mi

    // Çoklu branş desteği için yeni alanlar
    totalActiveMemberships?: number; // Toplam aktif üyelik sayısı
    allBranches?: string; // "Fitness, Crossfit, Pilates"
    combinedBranchDisplay?: string; // "Fitness(40), Crossfit(60)"
    hasMultipleBranches?: boolean; // Birden fazla branşa üye mi?
  }
  
  