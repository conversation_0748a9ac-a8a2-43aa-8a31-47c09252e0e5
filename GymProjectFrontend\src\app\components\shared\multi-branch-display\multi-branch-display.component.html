<!-- Compact Mode - Tek satırda gösterim -->
<div *ngIf="displayMode === 'compact'" class="compact-display">
  <span *ngFor="let branch of getActiveBranches(); let last = last" class="branch-item">
    <span 
      class="badge me-1" 
      [class]="getBranchColor(branch)"
      [title]="'En uzun kalan süre: ' + getMaxRemainingDaysByBranch(branch) + ' gün'"
    >
      {{ branch }}({{ getMaxRemainingDaysByBranch(branch) }})
    </span>
    <span *ngIf="!last" class="text-muted">, </span>
  </span>
</div>

<!-- Detailed Mode - Detaylı gösterim -->
<div *ngIf="displayMode === 'detailed'" class="detailed-display">
  <div class="row">
    <div *ngFor="let membership of memberships" class="col-md-6 col-lg-4 mb-3">
      <div class="card membership-detail-card h-100">
        <div class="card-header py-2" [class]="getBranchColor(membership.branch) + ' text-white'">
          <h6 class="mb-0">
            <i class="fas fa-dumbbell me-2"></i>
            {{ membership.branch }}
          </h6>
        </div>
        <div class="card-body py-2">
          <h6 class="card-title text-truncate" [title]="membership.typeName">
            {{ membership.typeName }}
          </h6>
          
          <!-- Kalan Gün -->
          <div class="mb-2">
            <small class="text-muted">Kalan Süre:</small><br>
            <span [class]="getRemainingDaysColor(membership.remainingDays)" class="fw-bold">
              {{ membership.remainingDays }} gün
            </span>
          </div>

          <!-- Tarih Bilgileri -->
          <div class="row text-sm">
            <div class="col-6">
              <small class="text-muted">Başlangıç:</small><br>
              <span>{{ formatDate(membership.startDate) }}</span>
            </div>
            <div class="col-6">
              <small class="text-muted">Bitiş:</small><br>
              <span>{{ formatDate(membership.endDate) }}</span>
            </div>
          </div>

          <!-- Son Ödeme -->
          <div *ngIf="membership.lastPaymentAmount > 0" class="mt-2">
            <small class="text-muted">Son Ödeme:</small><br>
            <span class="text-success fw-bold">{{ formatCurrency(membership.lastPaymentAmount) }}</span>
            <small class="text-muted ms-1">({{ membership.lastPaymentMethod }})</small>
          </div>

          <!-- Durum Badge'leri -->
          <div *ngIf="showBadges" class="mt-2">
            <span *ngIf="membership.isFrozen" class="badge bg-info me-1">
              <i class="fas fa-snowflake me-1"></i>Dondurulmuş
            </span>
            <span *ngIf="membership.isFutureStartDate" class="badge bg-warning me-1">
              <i class="fas fa-clock me-1"></i>Gelecek Tarihli
            </span>
            <span *ngIf="!membership.isActive" class="badge bg-secondary me-1">
              <i class="fas fa-pause me-1"></i>Pasif
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Özet Bilgi -->
  <div *ngIf="memberships.length > 1" class="summary-info mt-3">
    <div class="alert alert-info py-2">
      <div class="row text-center">
        <div class="col-4">
          <strong>{{ memberships.length }}</strong><br>
          <small class="text-muted">Toplam Üyelik</small>
        </div>
        <div class="col-4">
          <strong>{{ getActiveBranches().length }}</strong><br>
          <small class="text-muted">Farklı Branş</small>
        </div>
        <div class="col-4">
          <strong class="text-success">{{ getTotalRemainingDays() }}</strong><br>
          <small class="text-muted">Toplam Gün</small>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Boş Durum -->
<div *ngIf="memberships.length === 0" class="empty-state text-center py-3">
  <i class="fas fa-exclamation-circle text-muted fa-2x mb-2"></i>
  <p class="text-muted mb-0">Aktif üyelik bulunamadı</p>
</div>
