import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Observable } from 'rxjs';
import { map, startWith, debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { Member } from '../../../models/member';
import { MembershipType } from '../../../models/membershipType';
import { MemberService } from '../../../services/member.service';
import { MembershipService } from '../../../services/membership.service';
import { MembershipTypeService } from '../../../services/membership-type.service';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';

@Component({
  selector: 'app-membership-add',
  templateUrl: './membership-add.component.html',
  styleUrls: ['./membership-add.component.css'],
  standalone: false
})
export class MembershipAddComponent implements OnInit {
  membershipAddForm: FormGroup;
  members: Member[] = [];
  membershipTypes: MembershipType[] = [];
  filteredMembers: Observable<Member[]>;
  filteredMembershipTypes: Observable<MembershipType[]>;
  showBranchList: boolean = false;
  lastMembershipInfo: string | null = null;
  isSubmitting = false;
  isLoading = true; // Sayfa yüklenirken spinner göstermek için

  constructor(
    private formBuilder: FormBuilder,
    private memberService: MemberService,
    private membershipService: MembershipService,
    private membershipTypeService: MembershipTypeService,
    private toastrService: ToastrService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.createMembershipAddForm();
    this.loadInitialData();
    this.setupMemberAutocomplete();
    this.setupMembershipTypeAutocomplete();
    this.setupMembershipEndDateCalculation();

    // Başlangıç tarihini güncel tarih olarak ayarla
    this.setCurrentDateAsStartDate();
  }

  // Başlangıç tarihini güncel tarih olarak ayarlayan metot
  setCurrentDateAsStartDate(): void {
    const today = new Date();
    const formattedDate = today.toISOString().split('T')[0]; // YYYY-MM-DD formatı

    // Form kontrolüne değeri atama
    const startDateControl = this.membershipAddForm.get('startDate');
    if (startDateControl) {
      startDateControl.setValue(formattedDate);
    }
  }

  // Üyelik türlerinin boş olup olmadığını kontrol eden metot
  hasMembershipTypes(): boolean {
    // Üyelik türleri henüz yüklenmemişse veya en az bir üyelik türü varsa true döndür
    return !this.membershipTypes || this.membershipTypes.length > 0;
  }

  // Üyelik türü ekleme sayfasına yönlendirme
  navigateToMembershipTypeAdd(): void {
    this.router.navigate(['/membershiptype/add']);
  }

  createMembershipAddForm() {
    this.membershipAddForm = this.formBuilder.group({
      memberID: ['', Validators.required],
      membershipTypeID: ['', Validators.required],
      startDate: [new Date(), Validators.required],
      endDate: [''],
      day: ['', Validators.required], // Zorunlu yapıldı
      price: ['', Validators.required], // Zorunlu yapıldı
      PaymentMethod: ['', Validators.required],
      paymentStatus: ['Completed'] // Yeni alan eklendi
    });

    const memberIdControl = this.membershipAddForm.get('memberID');
    if (memberIdControl) {
      memberIdControl.valueChanges.subscribe(value => {
        if (value && typeof value === 'object') {
          this.getLastMembershipInfo(value.memberID);
        }
      });
    }
  }

  // Tüm başlangıç verilerini yükleyen metot
  loadInitialData() {
    this.isLoading = true;
    let membersLoaded = false;
    let membershipTypesLoaded = false;

    // Üyeleri yükle
    this.memberService.getMembers().subscribe({
      next: (response) => {
        this.members = response.data;
        membersLoaded = true;
        this.checkDataLoadingComplete(membersLoaded, membershipTypesLoaded);
      },
      error: (error) => {
        console.error('Üyeler yüklenirken hata oluştu:', error);
        this.toastrService.error('Üyeler yüklenirken bir hata oluştu.', 'Hata');
        membersLoaded = true;
        this.checkDataLoadingComplete(membersLoaded, membershipTypesLoaded);
      }
    });

    // Üyelik türlerini yükle
    this.membershipTypeService.getMembershipTypes().subscribe({
      next: (response) => {
        this.membershipTypes = response.data;
        membershipTypesLoaded = true;
        this.checkDataLoadingComplete(membersLoaded, membershipTypesLoaded);
      },
      error: (error) => {
        console.error('Üyelik türleri yüklenirken hata oluştu:', error);
        this.toastrService.error('Üyelik türleri yüklenirken bir hata oluştu.', 'Hata');
        membershipTypesLoaded = true;
        this.checkDataLoadingComplete(membersLoaded, membershipTypesLoaded);
      }
    });
  }

  // Veri yükleme tamamlanma kontrolü
  checkDataLoadingComplete(membersLoaded: boolean, membershipTypesLoaded: boolean) {
    if (membersLoaded && membershipTypesLoaded) {
      this.isLoading = false;
    }
  }

  getMembers() {
    this.memberService.getMembers().subscribe(response => {
      this.members = response.data;
    });
  }

  getMembershipTypes() {
    this.membershipTypeService.getMembershipTypes().subscribe(response => {
      this.membershipTypes = response.data;
    });
  }

  setupMemberAutocomplete() {
    const memberIdControl = this.membershipAddForm.get('memberID');
    if (memberIdControl) {
      this.filteredMembers = memberIdControl.valueChanges.pipe(
      
        distinctUntilChanged(),
        map(value => {
          if (typeof value === 'string') {
            if (!value.trim()) {
              return []; // Boş input durumunda boş liste döndür
            }
            return this._filterMembers(value);
          }
          return []; // Üye seçildiğinde dropdown'ı kapat
        })
      );
    }
  }

  setupMembershipTypeAutocomplete() {
    const membershipTypeControl = this.membershipAddForm.get('membershipTypeID');
    if (membershipTypeControl) {
      this.filteredMembershipTypes = membershipTypeControl.valueChanges.pipe(
        startWith(''),
        map(value => {
          // Always show all membership types
          return this.membershipTypes;
        })
      );

      membershipTypeControl.valueChanges.subscribe(value => {
        if (value) {
          const selectedType = this.membershipTypes.find(
            type => `${type.branch} - ${type.typeName}` === value
          );
          if (selectedType) {
            this.membershipAddForm.patchValue({
              day: selectedType.day,
              price: selectedType.price
            });
            this.calculateEndDate();
          } else if (typeof value === 'string' && value.trim() !== '') {
            // If user types something that's not a valid membership type,
            // reset the field to empty to force selection from dropdown
            setTimeout(() => {
              membershipTypeControl.setValue('');
            }, 0);
          }
        }
      });

      // Üyelik türü kontrolü - toast mesajı kaldırıldı
      // Artık sadece HTML'de uyarı gösterilecek, toast mesajı gösterilmeyecek
    }
  }

  setupMembershipEndDateCalculation() {
    const dayControl = this.membershipAddForm.get('day');
    const startDateControl = this.membershipAddForm.get('startDate');

    if (dayControl) {
      dayControl.valueChanges.subscribe(() => {
        this.calculateEndDate();
      });
    }

    if (startDateControl) {
      startDateControl.valueChanges.subscribe(() => {
        this.calculateEndDate();
      });
    }
  }

  private _filterMembers(value: string): Member[] {
    const filterValue = value.toLowerCase();
    return this.members.filter(member =>
      member.name.toLowerCase().includes(filterValue) ||
      member.phoneNumber.includes(filterValue)
    );
  }

  private _filterMembershipTypes(value: string): MembershipType[] {
    // Always return all membership types regardless of input
    return this.membershipTypes;
  }

  displayMembershipType(value: string): string {
    return value || '';
  }

  displayMember(member: Member): string {
    return member && member.name ? `${member.name} - ${member.phoneNumber}` : '';
  }

  calculateEndDate() {
    const startDateControl = this.membershipAddForm.get('startDate');
    const dayControl = this.membershipAddForm.get('day');

    if (startDateControl?.value && dayControl?.value) {
      const startDate = new Date(startDateControl.value);
      const endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + parseInt(dayControl.value));
      this.membershipAddForm.patchValue({ endDate: endDate });
    }
  }

  getLastMembershipInfo(memberId: number) {
    // Önce çoklu branş üyelik bilgilerini al
    this.membershipService.getMemberMultiBranchMemberships(memberId).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          const multiBranchData = response.data;
          if (multiBranchData.totalActiveMemberships > 0) {
            this.lastMembershipInfo = `Aktif üyelikler: ${multiBranchData.combinedBranchDisplay}`;

            // Çoklu branş varsa uyarı göster
            if (multiBranchData.hasMultipleBranches) {
              this.toastrService.info(
                `Bu üyenin ${multiBranchData.totalActiveMemberships} farklı branşta aktif üyeliği bulunmaktadır.`,
                'Çoklu Branş Üyeliği'
              );
            }
          } else {
            this.lastMembershipInfo = "Aktif üyelik bulunmuyor.";
          }
        } else {
          // Fallback: Eski yöntemle bilgi al
          this.getLastMembershipInfoFallback(memberId);
        }
      },
      error: (error) => {
        console.error('Çoklu branş bilgisi alınırken hata:', error);
        // Fallback: Eski yöntemle bilgi al
        this.getLastMembershipInfoFallback(memberId);
      }
    });
  }

  private getLastMembershipInfoFallback(memberId: number) {
    this.membershipService.getLastMembershipInfo(memberId).subscribe(
      response => {
        if (response.success) {
          const data = response.data;
          if (data.lastEndDate) {
            const endDate = new Date(data.lastEndDate);
            const now = new Date();
            if (endDate > now) {
              this.lastMembershipInfo = `Aktif üyelik mevcut. Bitiş: ${endDate.toLocaleDateString()}`;
            } else {
              this.lastMembershipInfo = `Son üyelik ${endDate.toLocaleDateString()} tarihinde sona erdi.`;
            }
          } else {
            this.lastMembershipInfo = "Daha önce üyelik kaydı bulunmuyor.";
          }
        }
      },
      (error: any) => {
        console.error('Error fetching last membership info:', error);
        this.lastMembershipInfo = null;
      }
    );
  }

  add() {
    if (!this.membershipAddForm.valid) {
      this.toastrService.error("Lütfen işaretli alanları doldurunuz", "Eksik Bilgi");

      // Tüm form kontrollerini dokunulmuş olarak işaretle
      Object.keys(this.membershipAddForm.controls).forEach(key => {
        const control = this.membershipAddForm.get(key);
        if (control) {
          control.markAsTouched();
          control.markAsDirty();
        }
      });

      // Eksik alanları vurgula ve titret
      this.highlightMissingFields();

      return;
    }

    this.isSubmitting = true;
    let membershipModel = Object.assign({}, this.membershipAddForm.value);

    // memberID işlemi
    if (typeof membershipModel.memberID === 'object') {
      membershipModel.memberID = membershipModel.memberID.memberID;
    }

    // PaymentStatus belirleme
    if (membershipModel.PaymentMethod === 'Borç') {
      membershipModel.paymentStatus = 'Pending';
    } else {
      membershipModel.paymentStatus = 'Completed';
    }

    // Membership type işlemleri
    const selectedType = this.membershipTypes.find(
      type => `${type.branch} - ${type.typeName}` === membershipModel.membershipTypeID
    );
    if (selectedType) {
      membershipModel.membershipTypeID = selectedType.membershipTypeID;
      // Kullanıcının girdiği day ve price değerleri korunacak, bu satırlar kaldırıldı.
      // membershipModel.day = selectedType.day;
      // membershipModel.price = selectedType.price;
    }

    // Tarih işlemleri
    if (membershipModel.startDate) {
      membershipModel.startDate = new Date(membershipModel.startDate);
    }
    if (membershipModel.endDate) {
      membershipModel.endDate = new Date(membershipModel.endDate);
    }

    // Akıllı yenileme kontrolü: Önce yenileme analizi yap
    this.membershipService.analyzeRenewalOptions(membershipModel.memberID, membershipModel.membershipTypeID).subscribe({
      next: (analysisResponse) => {
        if (analysisResponse.success) {
          const renewalData = analysisResponse.data;

          if (renewalData.isSamePackage && renewalData.isSameBranch) {
            // Aynı paket ve branş - kullanıcıya sor
            const confirmMessage = `Bu üyenin aynı branş ve pakette aktif üyeliği bulunmaktadır.

Seçenekler:
• UZAT: Mevcut üyeliği uzat (Önerilen)
• YENİ: Yeni üyelik oluştur

Mevcut üyeliği uzatmak istiyor musunuz?`;

            if (confirm(confirmMessage)) {
              // Smart renewal kullan
              this.performSmartRenewal(renewalData, membershipModel);
            } else {
              // Zorla yeni üyelik oluştur
              membershipModel.forceNewMembership = true;
              this.performRegularAdd(membershipModel);
            }
          } else {
            // Farklı paket veya branş - normal ekleme
            this.performRegularAdd(membershipModel);
          }
        } else {
          // Analiz başarısız - normal ekleme yap
          this.performRegularAdd(membershipModel);
        }
      },
      error: (error) => {
        console.error('Yenileme analizi hatası:', error);
        // Hata durumunda normal ekleme yap
        this.performRegularAdd(membershipModel);
      }
    });
  }

  private performSmartRenewal(renewalData: any, membershipModel: any) {
    const smartRenewalData = {
      memberID: membershipModel.memberID,
      currentMembershipID: renewalData.currentMembershipID,
      currentMembershipTypeID: renewalData.currentMembershipTypeID,
      newMembershipTypeID: membershipModel.membershipTypeID,
      currentEndDate: renewalData.currentEndDate,
      newStartDate: membershipModel.startDate,
      newEndDate: membershipModel.endDate,
      price: membershipModel.price,
      paymentMethod: membershipModel.PaymentMethod,
      day: membershipModel.day,
      isSameBranch: renewalData.isSameBranch,
      isSamePackage: renewalData.isSamePackage,
      renewalType: renewalData.renewalType
    };

    this.membershipService.smartRenewal(smartRenewalData).subscribe({
      next: (response) => {
        this.toastrService.success(response.message, "Akıllı Yenileme Başarılı");
        this.resetForm();
        this.isSubmitting = false;
      },
      error: (responseError) => {
        this.handleAddError(responseError);
      }
    });
  }

  private performRegularAdd(membershipModel: any) {
    this.membershipService.add(membershipModel).subscribe({
      next: (response) => {
        this.toastrService.success(response.message, "Başarılı");
        this.resetForm();
        this.isSubmitting = false;
      },
      error: (responseError) => {
        this.handleAddError(responseError);
      }
    });
  }

  private handleAddError(responseError: any) {
    if (responseError.error.Errors && responseError.error.Errors.length > 0) {
      responseError.error.Errors.forEach((error: { ErrorMessage: string | undefined; }) => {
        this.toastrService.error(error.ErrorMessage, "Doğrulama hatası");
      });
    } else {
      this.toastrService.error(responseError.error.message || "Bir hata oluştu", "Hata");
    }
    this.isSubmitting = false;
  }


  resetForm() {
    this.membershipAddForm.reset();

    // Başlangıç tarihini güncel tarih olarak ayarla
    const today = new Date();
    const formattedDate = today.toISOString().split('T')[0]; // YYYY-MM-DD formatı

    this.membershipAddForm.patchValue({
      startDate: formattedDate
    });

    this.lastMembershipInfo = null;
  }

  // Eksik alanları vurgulama metodu
  highlightMissingFields() {
    // Zorunlu alanları kontrol et ve eksik olanları belirle
    const missingFields: string[] = [];
    const requiredControls: (HTMLElement | null)[] = [];

    // Üye kontrolü
    const memberIdControl = this.membershipAddForm.get('memberID');
    if (memberIdControl?.invalid) {
      missingFields.push('Üye');
      requiredControls.push(document.getElementById('memberID'));
    }

    // Üyelik Türü kontrolü
    const membershipTypeIdControl = this.membershipAddForm.get('membershipTypeID');
    if (membershipTypeIdControl?.invalid) {
      missingFields.push('Üyelik Türü');
      requiredControls.push(document.getElementById('membershipTypeID'));
    }

    // Başlangıç Tarihi kontrolü
    const startDateControl = this.membershipAddForm.get('startDate');
    if (startDateControl?.invalid) {
      missingFields.push('Başlangıç Tarihi');
      requiredControls.push(document.getElementById('startDate'));
    }

    // Gün Sayısı kontrolü
    const dayControl = this.membershipAddForm.get('day');
    if (dayControl?.invalid) {
      missingFields.push('Gün Sayısı');
      requiredControls.push(document.getElementById('day'));
    }

    // Ücret kontrolü
    const priceControl = this.membershipAddForm.get('price');
    if (priceControl?.invalid) {
      missingFields.push('Ücret');
      requiredControls.push(document.getElementById('price'));
    }

    // Ödeme Türü kontrolü
    const paymentMethodControl = this.membershipAddForm.get('PaymentMethod');
    if (paymentMethodControl?.invalid) {
      missingFields.push('Ödeme Türü');
      requiredControls.push(document.getElementById('PaymentMethod'));
    }

    // Eksik alan sayısına göre mesaj göster
    if (missingFields.length > 0) {
      const fieldList = missingFields.join(', ');
      this.toastrService.warning(`Lütfen şu alanları doldurun: ${fieldList}`, 'Eksik Alanlar');

      // Eksik alanları görsel olarak vurgula ve titret
      setTimeout(() => {
        requiredControls.forEach((element, index) => {
          if (element) {
            // Titreşim animasyonu ekle
            element.classList.add('shake-animation');

            // İlk eksik alana kaydır
            if (index === 0) {
              element.scrollIntoView({ behavior: 'smooth', block: 'center' });
              element.focus(); // İlk alana odaklan
            }

            // Animasyonu bir süre sonra kaldır
            setTimeout(() => {
              element.classList.remove('shake-animation');
            }, 600);
          }
        });
      }, 100);
    }
  }

  isFormValid(): boolean {
    const memberIdControl = this.membershipAddForm.get('memberID');
    const membershipTypeIdControl = this.membershipAddForm.get('membershipTypeID');
    const startDateControl = this.membershipAddForm.get('startDate');
    const dayControl = this.membershipAddForm.get('day'); // Eklendi
    const priceControl = this.membershipAddForm.get('price'); // Eklendi
    const paymentMethodControl = this.membershipAddForm.get('PaymentMethod');

    // Tüm zorunlu alanların geçerliliğini kontrol et
    return !!(memberIdControl?.valid &&
             membershipTypeIdControl?.valid &&
             startDateControl?.valid &&
             dayControl?.valid && // Eklendi
             priceControl?.valid && // Eklendi
             paymentMethodControl?.valid);
  }

  // Güncel tarihi ISO formatında döndüren metot
  getCurrentDate(): string {
    const today = new Date();
    return today.toISOString().split('T')[0]; // YYYY-MM-DD formatında
  }
}