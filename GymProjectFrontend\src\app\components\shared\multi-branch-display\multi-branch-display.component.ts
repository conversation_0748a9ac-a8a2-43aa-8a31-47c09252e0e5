import { Component, Input, OnInit } from '@angular/core';
import { ActiveMembershipDetail } from '../../../models/multiBranchMembership';

@Component({
  selector: 'app-multi-branch-display',
  templateUrl: './multi-branch-display.component.html',
  styleUrls: ['./multi-branch-display.component.css'],
  standalone: false
})
export class MultiBranchDisplayComponent implements OnInit {
  @Input() memberships: ActiveMembershipDetail[] = [];
  @Input() displayMode: 'compact' | 'detailed' = 'compact';
  @Input() showBadges: boolean = true;

  constructor() {}

  ngOnInit(): void {}

  getBranchColor(branch: string): string {
    const colors: { [key: string]: string } = {
      'Fitness': 'bg-primary',
      'Crossfit': 'bg-success', 
      'Pilates': 'bg-info',
      'Yoga': 'bg-warning',
      'Boxing': 'bg-danger',
      'Swimming': 'bg-secondary'
    };
    return colors[branch] || 'bg-dark';
  }

  getRemainingDaysColor(remainingDays: number): string {
    if (remainingDays <= 0) return 'text-danger';
    if (remainingDays <= 7) return 'text-warning';
    if (remainingDays <= 30) return 'text-info';
    return 'text-success';
  }

  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString('tr-TR');
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(amount);
  }

  getCompactDisplay(): string {
    return this.memberships
      .map(m => `${m.branch}(${m.remainingDays})`)
      .join(', ');
  }

  getTotalRemainingDays(): number {
    return this.memberships.reduce((total, m) => total + m.remainingDays, 0);
  }

  getActiveBranches(): string[] {
    return [...new Set(this.memberships.map(m => m.branch))];
  }

  getMembershipsByBranch(branch: string): ActiveMembershipDetail[] {
    return this.memberships.filter(m => m.branch === branch);
  }

  getMaxRemainingDaysByBranch(branch: string): number {
    const branchMemberships = this.getMembershipsByBranch(branch);
    return Math.max(...branchMemberships.map(m => m.remainingDays));
  }
}
