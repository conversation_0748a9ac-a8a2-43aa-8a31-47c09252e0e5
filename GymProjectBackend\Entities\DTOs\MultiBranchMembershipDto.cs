using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class MultiBranchMembershipDto : IDto
    {
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public string PhoneNumber { get; set; }
        public int Gender { get; set; }
        public List<ActiveMembershipDetailDto> ActiveMemberships { get; set; }
        public string CombinedBranchDisplay { get; set; } // "Fitness(40), Crossfit(60)"
        public int TotalActiveMemberships { get; set; }
        public DateTime? EarliestEndDate { get; set; }
        public DateTime? LatestEndDate { get; set; }
        public bool HasMultipleBranches { get; set; }
    }

    public class ActiveMembershipDetailDto : IDto
    {
        public int MembershipID { get; set; }
        public int MembershipTypeID { get; set; }
        public string Branch { get; set; }
        public string TypeName { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int RemainingDays { get; set; }
        public bool IsActive { get; set; }
        public bool IsFrozen { get; set; }
        public bool IsFutureStartDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public decimal LastPaymentAmount { get; set; }
        public string LastPaymentMethod { get; set; }
    }
}
