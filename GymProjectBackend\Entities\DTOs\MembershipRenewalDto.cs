using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class MembershipRenewalDto : IDto
    {
        public int MemberID { get; set; }
        public int CurrentMembershipID { get; set; }
        public int CurrentMembershipTypeID { get; set; }
        public int NewMembershipTypeID { get; set; }
        public DateTime CurrentEndDate { get; set; }
        public DateTime NewStartDate { get; set; }
        public DateTime NewEndDate { get; set; }
        public decimal Price { get; set; }
        public string PaymentMethod { get; set; }
        public int Day { get; set; }
        public bool IsSameBranch { get; set; }
        public bool IsSamePackage { get; set; }
        public string RenewalType { get; set; } // "EXTEND", "UPGRADE", "CHANGE_BRANCH"
    }
}
