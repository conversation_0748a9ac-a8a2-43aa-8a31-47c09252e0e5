{"Version": 1, "Hash": "R6hYXKZ8LpL8RbEwPdG1hDAXXNEMOkCcN+rMjJ8wElg=", "Source": "WebAPI", "BasePath": "_content/WebAPI", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "WebAPI\\wwwroot", "Source": "WebAPI", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\GymProject - Kopya\\GymProjectBackend\\WebAPI\\wwwroot\\", "BasePath": "_content/WebAPI", "Pattern": "**"}], "Assets": [], "Endpoints": []}